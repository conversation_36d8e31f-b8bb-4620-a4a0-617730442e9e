permissionset 60000 "Ipek Pamuk Cust. IPK"
{
    Assignable = true;
    Caption = 'Ipek Pamuk Customizations', MaxLength = 30;
    Access = Internal;
    Permissions = tabledata "Customs Officer IPK" = RIMD,
        tabledata "DeletedPackageNoInfoLines IPK" = RIMD,
        tabledata "Inventory Adjustment Header" = RIMD,
        tabledata "Inventory Adjustment Line IPK" = RIMD,
        tabledata "Ipek Pamuk Activity Cue IPK" = RIMD,
        tabledata "Ipek Pamuk Setup IPK" = RIMD,
        tabledata "Item Certificate IPK" = RIMD,
        tabledata "Item Certificate Line IPK" = RIMD,
        tabledata "Load Header IPK" = RIMD,
        tabledata "Load Line IPK" = RIMD,
        tabledata "Load Method Type IPK" = RIMD,
        tabledata "Load Planning Line IPK" = RIMD,
        tabledata "Location - Quality Ctrl. Setup" = RIMD,
        tabledata "Lot Adjustment Line IPK" = RIMD,
        tabledata "Multiple QC Approval Line IPK" = RIMD,
        tabledata "Package Combine Header IPK" = RIMD,
        tabledata "Package Combine Line IPK" = RIMD,
        tabledata "Package Creation IPK" = RIMD,
        tabledata "Package No. Info. Line IPK" = RIMD,
        tabledata "Package Transfer Header IPK" = RIMD,
        tabledata "Package Transfer Line IPK" = RIMD,
        tabledata "Prod. Order Line Detail IPK" = RIMD,
        tabledata "Prod.Order Line - Lot No. IPK" = RIMD,
        tabledata "Warehouse Receipt Line Dtl IPK" = RIMD,
        table "Customs Officer IPK" = X,
        table "DeletedPackageNoInfoLines IPK" = X,
        table "Inventory Adjustment Header" = X,
        table "Inventory Adjustment Line IPK" = X,
        table "Ipek Pamuk Activity Cue IPK" = X,
        table "Ipek Pamuk Setup IPK" = X,
        table "Item Certificate IPK" = X,
        table "Item Certificate Line IPK" = X,
        table "Load Header IPK" = X,
        table "Load Line IPK" = X,
        table "Load Method Type IPK" = X,
        table "Load Planning Line IPK" = X,
        table "Location - Quality Ctrl. Setup" = X,
        table "Lot Adjustment Line IPK" = X,
        table "Multiple QC Approval Line IPK" = X,
        table "Package Combine Header IPK" = X,
        table "Package Combine Line IPK" = X,
        table "Package Creation IPK" = X,
        table "Package No. Info. Line IPK" = X,
        table "Package Transfer Header IPK" = X,
        table "Package Transfer Line IPK" = X,
        table "Prod. Order Line Detail IPK" = X,
        table "Prod.Order Line - Lot No. IPK" = X,
        table "Warehouse Receipt Line Dtl IPK" = X,
        report "Bill Of Lading IPK" = X,
        report "Customs Instructions IPK" = X,
        report "Packing List IPK" = X,
        report "Palette Label IPK" = X,
        report "Proforma Invoice IPK" = X,
        report "Sales Invoice IPK" = X,
        report "ShipmentAdviceIpk IPK" = X,
        report "Single Palette Label IPK" = X,
        codeunit "Inventory Adjustment IPK" = X,
        codeunit "Ipek Basic Functions IPK" = X,
        codeunit "Ipek Events IPK" = X,
        codeunit "Ipek Package Trans. Mgt. IPK" = X,
        codeunit "Ipek Production Management IPK" = X,
        codeunit "Ipek Purchase Management IPK" = X,
        codeunit "Ipek Quality Management IPK" = X,
        codeunit "Ipek Sales Management IPK" = X,
        page "Consumption Differences IPK" = X,
        page "Customs Officers IPK" = X,
        page "DeletedPackageNoInfoLines IPK" = X,
        page "Inventory Adjustment IPK" = X,
        page "Inventory Adjustment List IPK" = X,
        page "Inventory Adjustment Subpage" = X,
        page "Ipek Pamuk Activities IPK" = X,
        page "Ipek Pamuk Loading IPK" = X,
        page "Ipek Pamuk Quality Control IPK" = X,
        page "Ipek Pamuk Role Center IPK" = X,
        page "Ipek Pamuk Setup IPK" = X,
        page "Ipek Pamuk Transfer IPK" = X,
        page "Ipek Role Center Setup IPK" = X,
        page "Item Certificate Line IPK" = X,
        page "Item Certificates IPK" = X,
        page "Load Card IPK" = X,
        page "Load Line Subpage IPK" = X,
        page "Load Lines - Edit IPK" = X,
        page "Load Lines IPK" = X,
        page "Load List IPK" = X,
        page "Load Method Types IPK" = X,
        page "Load Planning Lines IPK" = X,
        page "Load Planning Subpage IPK" = X,
        page "Location - Quality Ctrl. Setup" = X,
        page "Lot Adjustment List IPK" = X,
        page "Multiple QC Lines Subpage IPK" = X,
        page "Package Combine IPK" = X,
        page "Package Combine List IPK" = X,
        page "Package Combine Subpage IPK" = X,
        page "Package Creation IPK" = X,
        page "Package No. Info. Line FactBox" = X,
        page "Package No. Info. Lines IPK" = X,
        page "Package No. Info. List IPK" = X,
        page "Package No. Info. Subpage IPK" = X,
        page "Package Transfer Order IPK" = X,
        page "Package Transfer Orders IPK" = X,
        page "Package Transfer Subpage IPK" = X,
        page "Palette Card IPK" = X,
        page "Process Multiple QC IPK" = X,
        page "Process Quality Control Doc." = X,
        page "Prod. Order Line Details IPK" = X,
        page "Prod. Order Lines IPK" = X,
        page "Prod.Order Line - Lot No. IPK" = X,
        page "Warehouse Receipt Line Details" = X,
        query "Inventory Report Ipek IPK" = X,
        query "Item Ledger Entry Query IPK" = X,
        query "Package No. Info. Line IPK" = X,
        query "Cust. Ledger Entry IPK" = X,
        query "G/L Entry IPK" = X,
        query "Item Ledger Entry IPK" = X,
        query "Value Entry IPK" = X,
        query "Vendor Ledger Entry IPK" = X,
        tabledata "Q.C. Mail Setup IPK" = RIMD,
        table "Q.C. Mail Setup IPK" = X,
        page "Q.C. Mail Setup IPK" = X,
        page "Production BOM Header IPK" = X,
        page "Package Transfer Details IPK" = X,
        report "ExchRate Adj. Acc. Detail IPK" = X,
        report "GEKAP Dataset IPK" = X,
        page "Posted Whse. Receipt Line IPK" = X,
        page "Production BOM Lines IPK" = X,
        page "Merge Inventory Adjustment IPK" = X,
        page "Reservation Entries IPK" = X,
        page "Inventory Adjustment Lines IPK" = X,
        report "Inventory Adjustment Lines IPK" = X,
        tabledata "Cutting Ratio By Type IPK" = RIMD,
        table "Cutting Ratio By Type IPK" = X,
        codeunit "Price List Management IPK" = X,
        page "Price List Line Card IPK" = X,
        codeunit "Automatic Family Creation IPK" = X,
        page "Cutting Ratio By Type IPK" = X,
        tabledata "Inventory Adj. Line Detail IPK" = RIMD,
        table "Inventory Adj. Line Detail IPK" = X,
        page "Family Lines IPK" = X,
        page "Package No. Info. Lines Lite" = X,
        page "Package Transfer Lines IPK" = X,
        report "Ledger Entry With Variants IPK" = X,
        page "Include Adjustment IPK" = X,
        tabledata "Item Price Dataset IPK" = RIMD,
        table "Item Price Dataset IPK" = X,
        codeunit "Item Price Dataset JQ IPK" = X,
        codeunit "Item Price Dataset Mgt IPK" = X,
        codeunit "Item Price Dataset Mngt. 2 IPK" = X,
        page "Item Price Dataset List IPK" = X,
        tabledata "Inventory Adjustment Item List" = RIMD,
        table "Inventory Adjustment Item List" = X,
        page "Inventory Adjustment Item List" = X,
        report "Package No. Info. Lines Report" = X;
}