codeunit 60030 "Item Price Dataset Mgt IPK"
{
    var
        ItemNotFoundErr: Label 'Item %1 does not exist.', Comment = '%1 = Item number';
        PurchaseProcessingCompletedMsg: Label 'Purchase processing completed for %1 purchase items. %2 existing records cleared, %3 monthly records created, %4 records populated with actual purchase invoice and credit memo data. Processing time: %5 minutes.', Comment = '%1 = Number of items, %2 = Number of cleared records, %3 = Number of monthly records, %4 = Number of populated records, %5 = Processing time in minutes';
        SalesProcessingCompletedMsg: Label 'Sales processing completed for %1 production items. %2 monthly records created, %3 records populated with actual invoice and credit memo data. Processing time: %4 minutes.', Comment = '%1 = Number of items, %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';
        SalesProcessingCompletedWithSkippedMsg: Label 'Sales processing completed for %1 production items. %2 monthly records created, %3 records populated with actual invoice and credit memo data. %4 items skipped due to manual prices. Processing time: %5 minutes.', Comment = '%1 = Number of items, %2 = Number of monthly records, %3 = Number of populated records, %4 = Number of skipped items, %5 = Processing time in minutes';
        StartDateMustBeSpecifiedErr: Label 'Start Date must be specified.';

    //PurchaseProcessingCompletedWithSkippedMsg: Label 'Purchase processing completed for %1 purchase items. %2 existing records cleared, %3 monthly records created, %4 records populated with actual purchase invoice and credit memo data. %5 items skipped due to manual prices. Processing time: %6 minutes.', Comment = '%1 = Number of items, %2 = Number of cleared records, %3 = Number of monthly records, %4 = Number of populated records, %5 = Number of skipped items, %6 = Processing time in minutes';


    local procedure ClearExistingRecordsForItemForSales(ItemNo: Code[20]; ForceClearManualPrices: Boolean): Boolean
    var
        ExistingRecord: Record "Item Price Dataset IPK";
        ManualPriceRecord: Record "Item Price Dataset IPK";
    begin
        // First check if any records exist for this item
        ExistingRecord.SetRange("Item No.", ItemNo);
        //ExistingRecord.SetRange("Entry Type", ExistingRecord."Entry Type"::Sale);

        if ExistingRecord.IsEmpty() then
            exit(true); // No records to clear, proceed with processing

        // Check if any existing records have manual prices or costs set
        ManualPriceRecord.SetRange("Item No.", ItemNo);
        //ManualPriceRecord.SetRange("Entry Type", ManualPriceRecord."Entry Type"::Sale);
        ManualPriceRecord.SetFilter("Manual Unit Price (ACY)", '<>%1', 0);

        // Also check for manual costs separately
        if ManualPriceRecord.IsEmpty() then begin
            ManualPriceRecord.SetRange("Manual Unit Price (ACY)"); // Clear previous filter
            ManualPriceRecord.SetFilter("Manual Unit Cost (ACY)", '<>%1', 0);
        end;

        if not ManualPriceRecord.IsEmpty() then
            if ForceClearManualPrices then begin
                // Clear manual prices for this item
                if ManualPriceRecord.FindSet() then
                    repeat
                        ManualPriceRecord."Manual Unit Price (ACY)" := 0;
                        ManualPriceRecord."Manual Unit Cost (ACY)" := 0;
                        ManualPriceRecord.Modify(true);
                    until ManualPriceRecord.Next() = 0;
                // Now delete all records for this item
                ExistingRecord.DeleteAll(true);
                exit(true);
            end else
                exit(false); // Manual prices exist, skip this item 4437

        // No manual prices found, safe to delete all records for this item
        ExistingRecord.DeleteAll(true);
        exit(true);
    end;

    local procedure ClearAllPurchaseRecordsWithZeroManualPrices(): Integer
    var
        ItemPriceDataset: Record "Item Price Dataset IPK";
        DeletedCount: Integer;
    begin
        // Set filters for purchase entries with zero manual prices and costs
        //ItemPriceDataset.SetRange("Entry Type", ItemPriceDataset."Entry Type"::Purchase);
        ItemPriceDataset.SetRange("Manual Unit Price (ACY)", 0);
        ItemPriceDataset.SetRange("Manual Unit Cost (ACY)", 0);

        // Count records before deletion
        DeletedCount := ItemPriceDataset.Count();

        // Delete all matching records
        if not ItemPriceDataset.IsEmpty() then
            ItemPriceDataset.DeleteAll(true);

        exit(DeletedCount);
    end;

    local procedure HasManualPriceForItemVariantMonth(ItemNo: Code[20]; VariantCode: Code[10]; SourcePriceMonth: Code[7]): Boolean
    var
        ManualPriceRecord: Record "Item Price Dataset IPK";
    begin
        // Check if this specific item+variant+month combination has manual prices or costs
        ManualPriceRecord.SetRange("Item No.", ItemNo);
        ManualPriceRecord.SetRange("Variant Code", VariantCode);
        ManualPriceRecord.SetRange("Source Price Month", SourcePriceMonth);
        //ManualPriceRecord.SetRange("Entry Type", ManualPriceRecord."Entry Type"::Purchase);
        ManualPriceRecord.SetFilter("Manual Unit Cost (ACY)", '<>%1', 0);

        exit(not ManualPriceRecord.IsEmpty());
    end;

    procedure ProcessAllProductionItems()
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        Item: Record Item;
        ProcessingTimeMs: BigInteger;
        StartDate: Date;
        EndTime: DateTime;
        StartTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        ItemCount: Integer;
        PopulatedRecords: Integer;
        SkippedItems: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
    begin
        // Get setup values
        IpekPamukSetup.GetRecordOnce();
        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;
        SkippedItems := 0;

        // Process all items with Replenishment System = Prod. Order
        Item.SetRange("Replenishment System", Item."Replenishment System"::"Prod. Order");
        if Item.FindSet() then
            repeat
                ItemCount += 1;

                // Step 1: Clear existing records for this item (with manual price check)
                if ClearExistingRecordsForItemForSales(Item."No.", false) then begin
                    // Step 2: Create monthly structure for this item
                    CreatedRecords := CreateCompleteMonthlyStructure(Item."No.");
                    TotalCreated += CreatedRecords;

                    // Step 3: Populate prices for this item
                    PopulatedRecords := PopulatePricesFromInvoicesPublic(Item."No.");
                    TotalPopulated += PopulatedRecords;
                end else
                    SkippedItems += 1;
            until Item.Next() = 0;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show comprehensive completion message with timing
        if SkippedItems > 0 then
            Message(SalesProcessingCompletedWithSkippedMsg,
                ItemCount, TotalCreated, TotalPopulated, SkippedItems, Round(ProcessingTimeMinutes, 0.01))
        else
            Message(SalesProcessingCompletedMsg,
                ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    procedure ProcessAllPurchaseItems()
    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        Item: Record Item;
        ProcessingTimeMs: BigInteger;
        StartDate: Date;
        EndTime: DateTime;
        StartTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        ClearedRecords: Integer;
        CreatedRecords: Integer;
        ItemCount: Integer;
        PopulatedRecords: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
    begin
        // Get setup values
        IpekPamukSetup.GetRecordOnce();
        StartDate := IpekPamukSetup."Item Price Dataset Start Date";
        if StartDate = 0D then
            Error(StartDateMustBeSpecifiedErr);

        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // Step 1: Bulk clear all purchase records with zero manual prices
        ClearedRecords := ClearAllPurchaseRecordsWithZeroManualPrices();

        // Process all items with Replenishment System = Purchase
        Item.SetRange("Replenishment System", Item."Replenishment System"::Purchase);
        if Item.FindSet() then
            repeat
                ItemCount += 1;

                // Step 2: Create monthly structure for this item (all combinations)
                CreatedRecords := CreateCompleteMonthlyStructureForPurchaseWithManualCheck(Item."No.");
                TotalCreated += CreatedRecords;

                // Step 3: Populate purchase prices for this item (skip manual price combinations)
                PopulatedRecords := PopulatePricesFromPurchaseEntriesWithManualCheck(Item."No.");
                TotalPopulated += PopulatedRecords;
            until Item.Next() = 0;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show comprehensive completion message with timing
        Message(PurchaseProcessingCompletedMsg,
            ItemCount, ClearedRecords, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    local procedure CreateCompleteMonthlyStructure(ItemNo: Code[20]): Integer
    var
        Item: Record Item;
        DatasetRecord: Record "Item Price Dataset IPK";
        LastRecord: Record "Item Price Dataset IPK";
        EndDate: Date;
        StartDate2: Date;
        i: Integer;
        NextEntryNo: Integer;
        MonthText: Text;
    begin
        // Get item description
        if not Item.Get(ItemNo) then
            Error(ItemNotFoundErr, ItemNo);

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Create records for current month count
        for i := 1 to WorkDate().Month() do begin
            DatasetRecord.Init();
            DatasetRecord."Entry No." := NextEntryNo;
            DatasetRecord."Item No." := ItemNo;
            DatasetRecord."Variant Code" := '';
            DatasetRecord."Description" := Item.Description;
            DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";

            // Format month as MM.YYYY
            if i < 10 then
                MonthText := '0' + Format(i) + '.2025'
            else
                MonthText := Format(i) + '.2025';

            DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
            DatasetRecord."Average Unit Price (ACY)" := 0;

            // Calculate start and end dates for the month
            StartDate2 := DMY2Date(1, i, 2025); // First day of month
            EndDate := CalcDate('<CM>', StartDate2); // Last day of month

            DatasetRecord."Start Date" := StartDate2;
            DatasetRecord."End Date" := EndDate;
            //DatasetRecord."Entry Type" := DatasetRecord."Entry Type"::Sale;

            DatasetRecord.Insert(true);
            NextEntryNo += 1;
        end;

        exit(WorkDate().Month());
    end;

    local procedure CreateCompleteMonthlyStructureForPurchaseWithManualCheck(ItemNo: Code[20]): Integer
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        DatasetRecord: Record "Item Price Dataset IPK";
        LastRecord: Record "Item Price Dataset IPK";
        VariantCode: Code[10];
        EndDate: Date;
        StartDate2: Date;
        CreatedCount: Integer;
        i: Integer;
        NextEntryNo: Integer;
        VariantCodes: List of [Code[10]];
        MonthText: Text;
    begin
        // Get item description
        if not Item.Get(ItemNo) then
            Error(ItemNotFoundErr, ItemNo);

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Find all unique Variant Codes for this item in Item Ledger Entries
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Purchase);
        if ItemLedgerEntry.FindSet() then
            repeat
                if not VariantCodes.Contains(ItemLedgerEntry."Variant Code") then
                    VariantCodes.Add(ItemLedgerEntry."Variant Code");
            until ItemLedgerEntry.Next() = 0;

        if VariantCodes.Count() = 0 then
            VariantCodes.Add(''); // Always at least one blank variant

        foreach VariantCode in VariantCodes do
            for i := 1 to WorkDate().Month() do begin
                // Format month as MM.YYYY
                if i < 10 then
                    MonthText := '0' + Format(i) + '.2025'
                else
                    MonthText := Format(i) + '.2025';

                // Check if this specific item+variant+month combination has manual prices
                if not HasManualPriceForItemVariantMonth(ItemNo, VariantCode, CopyStr(MonthText, 1, 7)) then begin
                    DatasetRecord.Init();
                    DatasetRecord."Entry No." := NextEntryNo;
                    DatasetRecord."Item No." := ItemNo;
                    DatasetRecord."Variant Code" := VariantCode;
                    DatasetRecord."Description" := Item.Description;
                    DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";

                    DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
                    DatasetRecord."Average Unit Cost (ACY)" := 0;

                    // Calculate start and end dates for the month
                    StartDate2 := DMY2Date(1, i, 2025); // First day of month
                    EndDate := CalcDate('<CM>', StartDate2); // Last day of month

                    DatasetRecord."Start Date" := StartDate2;
                    DatasetRecord."End Date" := EndDate;
                    //DatasetRecord."Entry Type" := DatasetRecord."Entry Type"::Purchase;

                    DatasetRecord.Insert(true);
                    CreatedCount += 1;
                end;
                NextEntryNo += 1;
            end;

        exit(CreatedCount);
    end;

    local procedure PopulatePricesFromPurchaseEntriesWithManualCheck(ItemNo: Code[20]): Integer
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        DatasetRecord: Record "Item Price Dataset IPK";
        ValueEntry: Record "Value Entry";
        ACYCurrencyCode: Code[10];
        CostAmountACY: Decimal;
        CostAmountLCY: Decimal;
        PreviousPrice: Decimal;
        PreviousPriceLCY: Decimal;
        TotalCostAmount: Decimal;
        TotalCostAmountLCY: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgCost: Decimal;
        WeightedAvgCostLCY: Decimal;
        PurchaseDataDict: Dictionary of [Text, Decimal];
        PurchaseDataLCYDict: Dictionary of [Text, Decimal];
        PurchaseQuantityDict: Dictionary of [Text, Decimal];
        PopulatedRecords: Integer;
        CurrentMonth: Text;
        PurchaseDataKey: Text;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // First pass: Collect actual purchase invoice data
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Purchase);
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetFilter("Quantity", '<>0');

        if ItemLedgerEntry.FindSet() then
            repeat
                // Get corresponding Value Entries for purchase invoice information
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Purchase);
                ValueEntry.SetFilter("Document Type", '%1|%2',
                    ValueEntry."Document Type"::"Purchase Invoice",
                    ValueEntry."Document Type"::"Purchase Credit Memo");
                ValueEntry.SetFilter("Cost Amount (Actual)", '<>0');
                ValueEntry.SetFilter("Invoiced Quantity", '<>0');

                if ValueEntry.FindSet() then
                    repeat
                        CurrentMonth := Format(ValueEntry."Posting Date", 0, '<Month,2>.<Year4>');
                        PurchaseDataKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Variant Code" + '|' + CurrentMonth;

                        // Skip if this item+variant+month has manual prices
                        if not HasManualPriceForItemVariantMonth(ItemLedgerEntry."Item No.", ItemLedgerEntry."Variant Code", CopyStr(CurrentMonth, 1, 7)) then begin
                            // Accumulate cost amounts (ACY and LCY) and quantities
                            // Get ACY amount - use ACY amount if available, otherwise fall back to LCY
                            CostAmountACY := ValueEntry."Cost Amount (Actual) (ACY)";
                            if CostAmountACY = 0 then
                                CostAmountACY := ValueEntry."Cost Amount (Actual)";

                            CostAmountLCY := ValueEntry."Cost Amount (Actual)";

                            // Accumulate ACY amounts
                            if PurchaseDataDict.ContainsKey(PurchaseDataKey) then
                                PurchaseDataDict.Set(PurchaseDataKey, PurchaseDataDict.Get(PurchaseDataKey) + CostAmountACY)
                            else
                                PurchaseDataDict.Add(PurchaseDataKey, CostAmountACY);

                            // Accumulate LCY amounts
                            if PurchaseDataLCYDict.ContainsKey(PurchaseDataKey) then
                                PurchaseDataLCYDict.Set(PurchaseDataKey, PurchaseDataLCYDict.Get(PurchaseDataKey) + CostAmountLCY)
                            else
                                PurchaseDataLCYDict.Add(PurchaseDataKey, CostAmountLCY);

                            // Accumulate quantities
                            if PurchaseQuantityDict.ContainsKey(PurchaseDataKey) then
                                PurchaseQuantityDict.Set(PurchaseDataKey, PurchaseQuantityDict.Get(PurchaseDataKey) + ValueEntry."Invoiced Quantity")
                            else
                                PurchaseQuantityDict.Add(PurchaseDataKey, ValueEntry."Invoiced Quantity");
                        end;
                    until ValueEntry.Next() = 0;
            until ItemLedgerEntry.Next() = 0;

        // Second pass: Update records with calculated purchase prices and propagate
        DatasetRecord.SetRange("Item No.", ItemNo);
        //DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Purchase);
        DatasetRecord.SetCurrentKey("Item No.", "Variant Code", "Source Price Month");
        if DatasetRecord.FindSet() then
            repeat
                // Skip records with manual prices
                if not HasManualPriceForItemVariantMonth(DatasetRecord."Item No.", DatasetRecord."Variant Code", DatasetRecord."Source Price Month") then begin
                    PurchaseDataKey := DatasetRecord."Item No." + '|' + DatasetRecord."Variant Code" + '|' + DatasetRecord."Source Price Month";

                    if PurchaseDataDict.ContainsKey(PurchaseDataKey) then begin
                        TotalCostAmount := PurchaseDataDict.Get(PurchaseDataKey);
                        TotalCostAmountLCY := PurchaseDataLCYDict.Get(PurchaseDataKey);
                        TotalQuantity := PurchaseQuantityDict.Get(PurchaseDataKey);

                        if TotalQuantity <> 0 then begin
                            WeightedAvgCost := TotalCostAmount / TotalQuantity;
                            WeightedAvgCostLCY := TotalCostAmountLCY / TotalQuantity;
                            DatasetRecord."Average Unit Cost (ACY)" := Abs(WeightedAvgCost);
                            DatasetRecord."Average Unit Cost (LCY)" := Abs(WeightedAvgCostLCY);
                            DatasetRecord."Currency Code" := ACYCurrencyCode;
                            DatasetRecord.Modify(true);
                            PopulatedRecords += 1;
                            PreviousPrice := WeightedAvgCost; // Store for propagation
                            PreviousPriceLCY := WeightedAvgCostLCY; // Store for propagation
                        end;
                    end else
                        // No purchase data for this month - use previous price if available
                        if (PreviousPrice <> 0) and (DatasetRecord."Average Unit Price (ACY)" = 0) then begin
                            DatasetRecord."Average Unit Price (ACY)" := Abs(PreviousPrice);
                            DatasetRecord."Average Unit Price (LCY)" := Abs(PreviousPriceLCY);
                            DatasetRecord."Currency Code" := ACYCurrencyCode;
                            DatasetRecord.Modify(true);
                        end;
                end;
            until DatasetRecord.Next() = 0;

        exit(PopulatedRecords);
    end;

    local procedure PopulatePricesFromInvoicesPublic(ItemNo: Code[20]): Integer
    var
        CurrencyExchangeRate: Record "Currency Exchange Rate";
        ItemLedgerEntry: Record "Item Ledger Entry";
        DatasetRecord: Record "Item Price Dataset IPK";
        ValueEntry: Record "Value Entry";
        ACYCurrencyCode: Code[10];
        PreviousPrice: Decimal;
        PreviousPriceLCY: Decimal;
        SalesAmountACY: Decimal;
        SalesAmountLCY: Decimal;
        TotalQuantity: Decimal;
        TotalSalesAmount: Decimal;
        TotalSalesAmountLCY: Decimal;
        WeightedAvgPrice: Decimal;
        WeightedAvgPriceLCY: Decimal;
        InvoiceDataDict: Dictionary of [Text, Decimal];
        InvoiceDataLCYDict: Dictionary of [Text, Decimal];
        InvoiceQuantityDict: Dictionary of [Text, Decimal];
        PopulatedRecords: Integer;
        CurrentMonth: Text;
        SalesDataKey: Text;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // First pass: Collect actual invoice data
        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Sale);
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetFilter("Quantity", '<>0');

        if ItemLedgerEntry.FindSet() then
            repeat
                // Get corresponding Value Entries for invoice information
                ValueEntry.SetRange("Item Ledger Entry No.", ItemLedgerEntry."Entry No.");
                ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
                ValueEntry.SetFilter("Document Type", '%1|%2',
                    ValueEntry."Document Type"::"Sales Invoice",
                    ValueEntry."Document Type"::"Sales Credit Memo");
                ValueEntry.SetFilter("Sales Amount (Actual)", '<>0');
                ValueEntry.SetFilter("Invoiced Quantity", '<>0');

                if ValueEntry.FindSet() then
                    repeat
                        CurrentMonth := Format(ValueEntry."Posting Date", 0, '<Month,2>.<Year4>');
                        SalesDataKey := ItemLedgerEntry."Item No." + '|' + ItemLedgerEntry."Variant Code" + '|' + CurrentMonth;

                        // Convert sales amount from LCY to ACY
                        if ACYCurrencyCode <> '' then
                            SalesAmountACY := CurrencyExchangeRate.ExchangeAmtLCYToFCY(
                                ValueEntry."Posting Date",
                                ACYCurrencyCode,
                                ValueEntry."Sales Amount (Actual)",
                                CurrencyExchangeRate.ExchangeRate(ValueEntry."Posting Date", ACYCurrencyCode))
                        else
                            SalesAmountACY := ValueEntry."Sales Amount (Actual)";

                        // Always get LCY amount
                        SalesAmountLCY := ValueEntry."Sales Amount (Actual)";

                        // Accumulate sales amounts (ACY) and quantities
                        if InvoiceDataDict.ContainsKey(SalesDataKey) then
                            InvoiceDataDict.Set(SalesDataKey, InvoiceDataDict.Get(SalesDataKey) + SalesAmountACY)
                        else
                            InvoiceDataDict.Add(SalesDataKey, SalesAmountACY);

                        // Accumulate sales amounts (LCY)
                        if InvoiceDataLCYDict.ContainsKey(SalesDataKey) then
                            InvoiceDataLCYDict.Set(SalesDataKey, InvoiceDataLCYDict.Get(SalesDataKey) + SalesAmountLCY)
                        else
                            InvoiceDataLCYDict.Add(SalesDataKey, SalesAmountLCY);

                        // Accumulate quantities
                        if InvoiceQuantityDict.ContainsKey(SalesDataKey) then
                            InvoiceQuantityDict.Set(SalesDataKey, InvoiceQuantityDict.Get(SalesDataKey) + ValueEntry."Invoiced Quantity")
                        else
                            InvoiceQuantityDict.Add(SalesDataKey, ValueEntry."Invoiced Quantity");
                    until ValueEntry.Next() = 0;
            until ItemLedgerEntry.Next() = 0;

        // Second pass: Update records with calculated prices and propagate
        DatasetRecord.SetRange("Item No.", ItemNo);
        //DatasetRecord.SetRange("Entry Type", DatasetRecord."Entry Type"::Sale);
        DatasetRecord.SetCurrentKey("Item No.", "Variant Code", "Source Price Month");

        if DatasetRecord.FindSet() then
            repeat
                SalesDataKey := DatasetRecord."Item No." + '|' + DatasetRecord."Variant Code" + '|' + DatasetRecord."Source Price Month";

                if InvoiceDataDict.ContainsKey(SalesDataKey) then begin
                    // Calculate weighted average price from actual data
                    TotalSalesAmount := InvoiceDataDict.Get(SalesDataKey);
                    TotalSalesAmountLCY := InvoiceDataLCYDict.Get(SalesDataKey);
                    TotalQuantity := InvoiceQuantityDict.Get(SalesDataKey);

                    if TotalQuantity <> 0 then begin
                        WeightedAvgPrice := TotalSalesAmount / TotalQuantity;
                        WeightedAvgPriceLCY := TotalSalesAmountLCY / TotalQuantity;
                        DatasetRecord."Average Unit Price (ACY)" := Abs(WeightedAvgPrice);
                        DatasetRecord."Average Unit Price (LCY)" := Abs(WeightedAvgPriceLCY);
                        DatasetRecord."Currency Code" := ACYCurrencyCode;
                        DatasetRecord.Modify(true);
                        PopulatedRecords += 1;
                        PreviousPrice := WeightedAvgPrice; // Store for propagation
                        PreviousPriceLCY := WeightedAvgPriceLCY; // Store for propagation
                    end;
                end else
                    // No invoice data for this month - use previous price if available
                    if (PreviousPrice <> 0) and (DatasetRecord."Average Unit Price (ACY)" = 0) then begin
                        DatasetRecord."Average Unit Price (ACY)" := Abs(PreviousPrice);
                        DatasetRecord."Average Unit Price (LCY)" := Abs(PreviousPriceLCY);
                        DatasetRecord."Currency Code" := ACYCurrencyCode;
                        DatasetRecord.Modify(true);
                    end;
            until DatasetRecord.Next() = 0;

        exit(PopulatedRecords);
    end;

    local procedure GetACYCurrencyCode(): Code[10]
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
    begin
        if GeneralLedgerSetup.Get() then
            exit(GeneralLedgerSetup."Additional Reporting Currency");

        exit('');
    end;
}
