report 60012 "Package No. Info. Lines Report"
{
    ApplicationArea = All;
    Caption = 'Package No. Info Lines Report';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(PackageNoInfoLineIPK; "Package No. Info. Line IPK")
        {
            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(Description; Description)
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(PackageNo; "Package No.")
            {
            }
            column(LineNo; "Line No.")
            {
            }
            column(LineItemNo; "Line Item No.")
            {
            }
            column(LineVariantCode; "Line Variant Code")
            {
            }
            column(TotalQtyonLocation; "Total Qty. on Location")
            {
            }
            column(ProductionLocation; "Production Location")
            {
            }
            column(SourcePackageNoIPK; "Source Package No. IPK")
            {
            }
            column(LastAdjustment; "Last Adjustment")
            {
            }
            column(TotalQtyonLocPack; "Total Qty. on Loc. Pack")
            {
            }
            column(WarehouseShipmentNo; "Warehouse Shipment No.")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
        }
    }

}