enum 60011 "Package Transfer Type IPK"
{
    Extensible = true;

    value(0; " ")
    {
        Caption = ' ', Locked = true;
    }
    value(1; Default)
    {
        Caption = 'Default';
    }
    value(2; "Package Combine")
    {
        Caption = 'Package Combine';
    }
    value(3; "Quality Control")
    {
        Caption = 'Quality Control';
    }
    value(4; "Inventory Adjustment")
    {
        Caption = 'Inventory Adjustment';
    }
}