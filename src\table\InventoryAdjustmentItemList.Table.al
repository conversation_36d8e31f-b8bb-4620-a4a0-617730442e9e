table 60028 "Inventory Adjustment Item List"
{
    Caption = 'Inventory Adjustment Item List';
    DataClassification = SystemMetadata;
    DrillDownPageId = "Inventory Adjustment Item List";
    LookupPageId = "Inventory Adjustment Item List";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Editable = false;
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(4; Status; Enum "Inventory Adj. Status IPK")
        {
            Caption = 'Status';
            ToolTip = 'Specifies the status of the inventory adjustment.';
            FieldClass = FlowField;
            Editable = false;
            AllowInCustomizations = Always;
            CalcFormula = lookup("Inventory Adjustment Header".Status where("No." = field("Document No.")));
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        InvAdjItemLine: Record "Inventory Adjustment Item List";
    begin
        InvAdjItemLine.SetRange("Document No.", Rec."Document No.");
        if InvAdjItemLine.FindLast() then
            Rec."Line No." := InvAdjItemLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}