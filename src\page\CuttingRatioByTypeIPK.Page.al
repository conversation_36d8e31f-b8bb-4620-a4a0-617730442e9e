page 60065 "Cutting Ratio By Type IPK"
{
    ApplicationArea = All;
    Caption = 'Cutting Ratio By Type';
    PageType = List;
    SourceTable = "Cutting Ratio By Type IPK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Cutting Category Type"; Rec."Cutting Category Type")
                {
                }
                field("Cutting Ratio"; Rec."Cutting Ratio")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Create Automatic Families")
            {
                Caption = 'Create Automatic Families';
                ToolTip = 'Creates automatic families for items with waterjet Production BOM components.';
                Image = CreateForm;

                trigger OnAction()
                var
                    AutomaticFamilyCreation: Codeunit "Automatic Family Creation IPK";
                begin
                    if Confirm('Do you want to create automatic families for all items with waterjet Production BOM components?', false) then begin
                        AutomaticFamilyCreation.CreateAutomaticFamilies();
                        Message('Automatic family creation process completed. Check the log for details.');
                    end;
                end;
            }
            action("Delete All Families")
            {
                Caption = 'Delete All Families (Test)';
                ToolTip = 'Deletes all families in the system - for testing purposes only.';
                Visible = false;
                Image = DeleteAllBreakpoints;

                trigger OnAction()
                var
                    AutomaticFamilyCreation: Codeunit "Automatic Family Creation IPK";
                begin
                    AutomaticFamilyCreation.DeleteAllFamilies();
                end;
            }
        }
    }
}