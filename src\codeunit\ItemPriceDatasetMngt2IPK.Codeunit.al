codeunit 60014 "Item Price Dataset Mngt. 2 IPK"
{
    var
        ProcessingCompletedMsg: Label 'Processing completed for %1 items. %2 monthly records created, %3 records populated with cost data. Processing time: %4 minutes.', Comment = '%1 = Number of items, %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';

    /// <summary>
    /// Main procedure to process all items with Item Ledger Entries and create monthly cost dataset
    /// </summary>
    procedure ProcessAllItemsWithLedgerEntries()
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        ProcessingTimeMs: BigInteger;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        ItemCount: Integer;
        TotalCreated: Integer;
        TotalPopulated: Integer;
    begin
        // Record start time
        StartTime := CurrentDateTime();

        // Initialize counters
        TotalCreated := 0;
        TotalPopulated := 0;
        ItemCount := 0;

        // Process all items that have Item Ledger Entries
        Item.SetRange(Blocked, false);
        if Item.FindSet() then
            repeat
                // Check if item has any Item Ledger Entry records
                ItemLedgerEntry.SetRange("Item No.", Item."No.");
                if not ItemLedgerEntry.IsEmpty() then begin
                    ItemCount += 1;

                    // Create monthly structure for this item
                    CreatedRecords := CreateMonthlyStructureForItem(Item."No.");
                    TotalCreated += CreatedRecords;

                    // Populate cost data for this item  
                    //PopulatedRecords := PopulateCostDataForItem(Item."No.");
                    TotalPopulated += PopulatedRecords;
                end;
            until Item.Next() = 0;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show completion message with timing
        Message(ProcessingCompletedMsg,
            ItemCount, TotalCreated, TotalPopulated, Round(ProcessingTimeMinutes, 0.01));
    end;

    /// <summary>
    /// Test procedure to process a single item for testing purposes
    /// </summary>
    procedure ProcessSingleItemForTesting(ItemNo: Code[20])
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        DatasetRecord: Record "Item Price Dataset IPK";
        ProcessingTimeMs: BigInteger;
        StartTime: DateTime;
        EndTime: DateTime;
        ProcessingTimeMinutes: Decimal;
        CreatedRecords: Integer;
        PopulatedRecords: Integer;
        TestCompletedMsg: Label 'Test processing completed for item %1. %2 monthly records created, %3 records populated with cost data. Processing time: %4 minutes.', Comment = '%1 = Item No., %2 = Number of monthly records, %3 = Number of populated records, %4 = Processing time in minutes';
    begin
        // Record start time
        StartTime := CurrentDateTime();

        // Check if item exists and has ledger entries
        if not Item.Get(ItemNo) then begin
            Message('Item %1 does not exist.', ItemNo);
            exit;
        end;

        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        if ItemLedgerEntry.IsEmpty() then begin
            Message('Item %1 has no Item Ledger Entries.', ItemNo);
            exit;
        end;

        // First, clear any existing dataset records for this item
        DatasetRecord.SetRange("Item No.", ItemNo);
        if not DatasetRecord.IsEmpty() then
            DatasetRecord.DeleteAll(true);

        // Create monthly structure for this item FIRST
        CreatedRecords := CreateMonthlyStructureForItem(ItemNo);

        // Then populate cost data for each dataset record individually
        PopulatedRecords := 0;
        DatasetRecord.SetRange("Item No.", ItemNo);
        if DatasetRecord.FindSet() then
            repeat
                if CalculateCostForDatasetRecord(DatasetRecord) then
                    PopulatedRecords += 1;
            until DatasetRecord.Next() = 0;

        // Record end time and calculate duration
        EndTime := CurrentDateTime();
        ProcessingTimeMs := EndTime - StartTime;
        ProcessingTimeMinutes := ProcessingTimeMs / 60000; // Convert milliseconds to minutes

        // Show completion message with timing
        Message(TestCompletedMsg,
            ItemNo, CreatedRecords, PopulatedRecords, Round(ProcessingTimeMinutes, 0.01));
    end;

    /// <summary>
    /// Creates monthly records from 01.01.2025 to current month for given item
    /// </summary>
    local procedure CreateMonthlyStructureForItem(ItemNo: Code[20]): Integer
    var
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        DatasetRecord: Record "Item Price Dataset IPK";
        LastRecord: Record "Item Price Dataset IPK";
        VariantCode: Code[10];
        StartDate: Date;
        EndDate: Date;
        CreatedCount: Integer;
        MonthNo: Integer;
        NextEntryNo: Integer;
        VariantCodes: List of [Code[10]];
        MonthText: Text;
        CurrentMonth: Integer;
    begin
        // Get item description
        if not Item.Get(ItemNo) then
            exit(0);

        // Get current month
        CurrentMonth := Date2DMY(WorkDate(), 2);

        // Get next Entry No.
        if LastRecord.FindLast() then
            NextEntryNo := LastRecord."Entry No." + 1
        else
            NextEntryNo := 1;

        // Find all unique Variant Codes for this item in Item Ledger Entries
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        if ItemLedgerEntry.FindSet() then
            repeat
                if not VariantCodes.Contains(ItemLedgerEntry."Variant Code") then
                    VariantCodes.Add(ItemLedgerEntry."Variant Code");
            until ItemLedgerEntry.Next() = 0;

        // If no variants found, add empty variant
        if VariantCodes.Count() = 0 then
            VariantCodes.Add('');

        // Create records for each month from January to current month in 2025
        foreach VariantCode in VariantCodes do
            for MonthNo := 1 to CurrentMonth do begin
                // Format month as MM.YYYY
                if MonthNo < 10 then
                    MonthText := '0' + Format(MonthNo) + '.2025'
                else
                    MonthText := Format(MonthNo) + '.2025';

                // Calculate start and end dates for the month
                StartDate := DMY2Date(1, MonthNo, 2025); // First day of month
                EndDate := CalcDate('<CM>', StartDate); // Last day of month

                // Create dataset record
                DatasetRecord.Init();
                DatasetRecord."Entry No." := NextEntryNo;
                DatasetRecord."Item No." := ItemNo;
                DatasetRecord."Variant Code" := VariantCode;
                DatasetRecord."Description" := Item.Description;
                DatasetRecord."Base Unit of Measure Code" := Item."Base Unit of Measure";
                DatasetRecord."Source Price Month" := CopyStr(MonthText, 1, 7);
                DatasetRecord."Start Date" := StartDate;
                DatasetRecord."End Date" := EndDate;
                DatasetRecord."Average Unit Cost (ACY)" := 0;
                DatasetRecord."Average Unit Cost (LCY)" := 0;

                DatasetRecord.Insert(true);

                // Calculate cost for the newly created dataset record
                CalculateCostForDatasetRecord(DatasetRecord);
                CreatedCount += 1;
                NextEntryNo += 1;
            end;

        exit(CreatedCount);
    end;

    local procedure GetACYCurrencyCode(): Code[10]
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
    begin
        if GeneralLedgerSetup.Get() then
            exit(GeneralLedgerSetup."Additional Reporting Currency");

        exit('');
    end;

    /// <summary>
    /// Gets cost data from previous months for the same item and variant
    /// Returns true if previous month data is found and applied
    /// </summary>
    local procedure GetPreviousMonthCostData(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        PreviousDatasetRecord: Record "Item Price Dataset IPK";
    begin
        // Look for dataset record with same item and variant for dates before current record
        PreviousDatasetRecord.Reset();
        PreviousDatasetRecord.SetRange("Item No.", DatasetRecord."Item No.");
        PreviousDatasetRecord.SetRange("Variant Code", DatasetRecord."Variant Code");
        PreviousDatasetRecord.SetFilter("Start Date", '<%1', DatasetRecord."Start Date");
        PreviousDatasetRecord.SetCurrentKey("Start Date");

        // Find all records and identify the one with the latest date that has cost data
        if PreviousDatasetRecord.FindLast() then begin
            DatasetRecord."Average Unit Cost (ACY)" := PreviousDatasetRecord."Average Unit Cost (ACY)";
            DatasetRecord."Average Unit Cost (LCY)" := PreviousDatasetRecord."Average Unit Cost (LCY)";
            DatasetRecord."Currency Code" := PreviousDatasetRecord."Currency Code";
            DatasetRecord.Modify(true);
            exit(true); // Successfully found and applied previous month data
        end;

        exit(false); // No previous month data found
    end;    /// <summary>
            /// Calculates cost for a single dataset record using its own parameters
            /// </summary>
    local procedure CalculateCostForDatasetRecord(var DatasetRecord: Record "Item Price Dataset IPK"): Boolean
    var
        ValueEntry: Record "Value Entry";
        ACYCurrencyCode: Code[10];
        CostAmountACY: Decimal;
        CostAmountLCY: Decimal;
        TotalCostAmountACY: Decimal;
        TotalCostAmountLCY: Decimal;
        TotalQuantity: Decimal;
        WeightedAvgCostACY: Decimal;
        WeightedAvgCostLCY: Decimal;
    begin
        // Get ACY currency code
        ACYCurrencyCode := GetACYCurrencyCode();

        // Initialize totals
        TotalCostAmountACY := 0;
        TotalCostAmountLCY := 0;
        TotalQuantity := 0;

        // Filter Value Entries for this specific dataset record
        ValueEntry.Reset();
        ValueEntry.SetRange("Item No.", DatasetRecord."Item No.");

        // Handle Variant Code - if dataset has blank variant, filter for blank; otherwise use specific variant
        if DatasetRecord."Variant Code" = '' then
            ValueEntry.SetRange("Variant Code", '')
        else
            ValueEntry.SetRange("Variant Code", DatasetRecord."Variant Code");

        ValueEntry.SetRange("Posting Date", DatasetRecord."Start Date", DatasetRecord."End Date");

        // Filter for Purchase Invoice, Purchase Credit Memo, and blank document types
        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
            ValueEntry."Document Type"::"Purchase Invoice",
            ValueEntry."Document Type"::"Purchase Credit Memo",
            ValueEntry."Document Type"::" ");

        // Filter for Purchase, Positive Adj, Negative Adj entry types
        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2|%3|%4',
            ValueEntry."Item Ledger Entry Type"::Purchase,
            ValueEntry."Item Ledger Entry Type"::"Positive Adjmt.",
            ValueEntry."Item Ledger Entry Type"::"Negative Adjmt.",
            ValueEntry."Item Ledger Entry Type"::Output);
        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);
        ValueEntry.SetRange(Adjustment, false);

        // Collect cost data from Value Entries
        if ValueEntry.FindSet() then
            repeat
                // Get ACY amount - use ACY amount if available, otherwise fall back to LCY
                CostAmountACY := ValueEntry."Cost Amount (Actual) (ACY)";
                if CostAmountACY = 0 then
                    CostAmountACY := ValueEntry."Cost Amount (Actual)";

                CostAmountLCY := ValueEntry."Cost Amount (Actual)";

                // Accumulate amounts and quantities
                TotalCostAmountACY += CostAmountACY;
                TotalCostAmountLCY += CostAmountLCY;
                TotalQuantity += ValueEntry."Valued Quantity";
            until ValueEntry.Next() = 0;

        // Calculate weighted average costs if we have quantity
        if TotalQuantity <> 0 then begin
            WeightedAvgCostACY := TotalCostAmountACY / TotalQuantity;
            WeightedAvgCostLCY := TotalCostAmountLCY / TotalQuantity;

            // Update the dataset record
            DatasetRecord."Average Unit Cost (ACY)" := Abs(WeightedAvgCostACY);
            DatasetRecord."Average Unit Cost (LCY)" := Abs(WeightedAvgCostLCY);
            DatasetRecord."Currency Code" := ACYCurrencyCode;
            DatasetRecord.Modify(true);

            exit(true); // Successfully calculated and updated
        end;

        // If no current month data found, try to get data from previous months
        if GetPreviousMonthCostData(DatasetRecord) then
            exit(true); // Successfully used previous month data

        exit(false); // No data found from current or previous months
    end;
}