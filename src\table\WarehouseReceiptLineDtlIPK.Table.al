table 60005 "Warehouse Receipt Line Dtl IPK"
{
    Caption = 'Warehouse Receipt Line Details';
    DataClassification = CustomerContent;
    LookupPageId = "Warehouse Receipt Line Details";
    DrillDownPageId = "Warehouse Receipt Line Details";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the document number.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the line number within the document.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number within the document line.';
        }
        field(4; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the package number.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the item number.';
            trigger OnValidate()
            begin
                Rec."Item Description" := IpekBasicFunctions.GetItemDescriptionFromItemNo(Rec."Item No.");
            end;
        }
        field(6; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies a description of the item.';
        }
        field(7; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the quantity of the item.';
        }
        field(8; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
        }
        field(9; "Item Tracking Info Assignd IPK"; Boolean)
        {
            Caption = 'Item Tracking Info. Assigned';
            ToolTip = 'Specifies whether the item tracking information is assigned.';
        }
        field(10; Recieved; Boolean)
        {
            Caption = 'Recieved';
            ToolTip = 'Specifies whether the Recieved is assigned.';
        }
        field(11; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies whether the Variant Code is assigned.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
        key(key1; Recieved)
        {

        }

    }
    trigger OnInsert()
    var
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK";
    begin
        WarehouseReceiptLineDtl.SetRange("Document No.", Rec."Document No.");
        WarehouseReceiptLineDtl.SetRange("Document Line No.", Rec."Document Line No.");
        if WarehouseReceiptLineDtl.FindLast() then
            Rec."Line No." := WarehouseReceiptLineDtl."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageNoInformation.Get('', '', Rec."Package No.") then
            PackageNoInformation.Delete(true);
    end;

    var
        IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
}
