page 60032 "Item Price Dataset List IPK"
{
    ApplicationArea = All;
    Caption = 'Item Price Dataset';
    PageType = List;
    SourceTable = "Item Price Dataset IPK";
    UsageCategory = Lists;
    Editable = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Entry No."; Rec."Entry No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Base Unit of Measure Code"; Rec."Base Unit of Measure Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Source Price Month"; Rec."Source Price Month")
                {
                }
                // field("Average Unit Cost (LCY)"; Rec."Average Unit Cost (LCY)")
                // {
                // }
                field("Average Unit Cost"; Rec."Average Unit Cost (ACY)")
                {
                }
                field("Manual Unit Cost"; Rec."Manual Unit Cost (ACY)")
                {
                }
                field("Use Manual Price"; Rec."Use Manual Price")
                {
                }
                field("Average Unit Price"; Rec."Average Unit Price (ACY)")
                {
                }
                // field("Average Unit Price (LCY)"; Rec."Average Unit Price (LCY)")
                // {
                // }
                field("Manual Unit Price"; Rec."Manual Unit Price (ACY)")
                {
                }
                field("Currency Code"; Rec."Currency Code")
                {
                }
                field("Start Date"; Rec."Start Date")
                {
                }
                field("End Date"; Rec."End Date")
                {
                }
                // field("Entry Type"; Rec."Entry Type")
                // {
                // }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            group(DataProcessing)
            {
                Caption = 'Data Processing';

                action(SalesProductionItems)
                {
                    ApplicationArea = All;
                    Caption = 'Caption', comment = 'TRK="YourLanguageCaption"';
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    Image = Image;

                    trigger OnAction()
                    var
                        ItemPriceDatasetMgtIPK: Codeunit "Item Price Dataset Mgt IPK";
                    begin
                        ItemPriceDatasetMgtIPK.ProcessAllProductionItems();
                    end;

                }
                action(ProcessAllItemsWithLedgerEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Process All Items (New Method)';
                    Image = Calculate;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'Process cost dataset for all items with Item Ledger Entries using the new method (creates single records covering both cost and price perspective).';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMngt2: Codeunit "Item Price Dataset Mngt. 2 IPK";
                    begin
                        if Confirm('This will process cost dataset for all items with Item Ledger Entries using the new method. Continue?') then
                            ItemPriceDatasetMngt2.ProcessAllItemsWithLedgerEntries();
                    end;
                }
                action(ProcessSingleItemTest)
                {
                    ApplicationArea = All;
                    Caption = 'Test Process I003735 Only';
                    Image = TestDatabase;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = false;
                    PromotedOnly = true;
                    ToolTip = 'Process cost dataset for item I003735 only for testing purposes.';

                    trigger OnAction()
                    var
                        ItemPriceDatasetMngt2: Codeunit "Item Price Dataset Mngt. 2 IPK";
                    begin
                        if Confirm('This will process cost dataset for item I003735 only for testing. Continue?') then
                            ItemPriceDatasetMngt2.ProcessSingleItemForTesting('İ000126');
                    end;
                }
            }
            group(Related)
            {
                Caption = 'Related';

                action(ShowValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Sales)';
                    Image = ValueLedger;
                    Promoted = true;
                    PromotedCategory = Report;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'View the value entries (sales invoices and credit memos) that were used to calculate the average price for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(NoItemSelectedErrLbl);

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        // Filter for Sale entry type
                        ValueEntry.SetRange("Item Ledger Entry Type", ValueEntry."Item Ledger Entry Type"::Sale);
                        // Filter for Sales Invoice and Sales Credit Memo document types
                        ValueEntry.SetFilter("Document Type", '%1|%2',
                            ValueEntry."Document Type"::"Sales Invoice",
                            ValueEntry."Document Type"::"Sales Credit Memo");
                        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);


                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
                action(ShowPurchaseValueEntries)
                {
                    ApplicationArea = All;
                    Caption = 'Value Entries (Purchase)';
                    Image = Purchase;
                    Promoted = true;
                    PromotedCategory = Report;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                    ToolTip = 'View the value entries (purchase invoices, credit memos, and adjustments) that were used to calculate the average cost for this dataset record.';

                    trigger OnAction()
                    var
                        ValueEntry: Record "Value Entry";
                        ValueEntriesPage: Page "Value Entries";
                    begin
                        if Rec."Item No." = '' then
                            Error(NoItemSelectedErrLbl);

                        ValueEntry.SetRange("Item No.", Rec."Item No.");
                        if Rec."Variant Code" <> '' then
                            ValueEntry.SetRange("Variant Code", Rec."Variant Code");
                        if (Rec."Start Date" <> 0D) and (Rec."End Date" <> 0D) then
                            ValueEntry.SetRange("Posting Date", Rec."Start Date", Rec."End Date");
                        // Filter for Purchase, Positive Adj, Negative Adj entry types
                        ValueEntry.SetFilter("Item Ledger Entry Type", '%1|%2|%3|%4',
                            ValueEntry."Item Ledger Entry Type"::Purchase,
                            ValueEntry."Item Ledger Entry Type"::"Positive Adjmt.",
                            ValueEntry."Item Ledger Entry Type"::"Negative Adjmt.",
                            ValueEntry."Item Ledger Entry Type"::Output);
                        // Filter for Purchase Invoice, Purchase Credit Memo, and blank document types
                        ValueEntry.SetFilter("Document Type", '%1|%2|%3',
                            ValueEntry."Document Type"::"Purchase Invoice",
                            ValueEntry."Document Type"::"Purchase Credit Memo",
                            ValueEntry."Document Type"::" ");
                        ValueEntry.SetFilter("Entry Type", '<>%1', ValueEntry."Entry Type"::Rounding);

                        ValueEntry.SetRange(Adjustment, false);


                        ValueEntriesPage.SetTableView(ValueEntry);
                        ValueEntriesPage.Run();
                    end;
                }
            }
        }
    }
    var
        NoItemSelectedErrLbl: Label 'No item selected.';
}
