codeunit 60006 "Ipek Package Trans. Mgt. IPK"
{
    SingleInstance = true;
    procedure ProcessBarcode(var PackageTransferHeader: Record "Package Transfer Header IPK")
    var
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        // Location: Record Location;
        PackageNoInformation: Record "Package No. Information";
        PackageTransferLine: Record "Package Transfer Line IPK";
        QualityControlHeaderQCM: Record "Quality Control Header QCM";
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
        PackageAlreadyReadErr: Label 'This package has already been read in this package transfer order, please try a different package.';
        PackageBlockedErr: Label 'Selected Package is currently blocked';
        PackageInventoryAdjustmentLbl: Label 'This Package:%1 , has a Item that currently on the Inventory Adjustment, Please contact with your supervisor.', Comment = '%1="Package No. Info. Line IPK"."Package No."';
    begin
        if PackageTransferHeader.Barcode = '' then
            exit;

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.SetRange("Package No.", PackageTransferHeader.Barcode);

        if not PackageTransferLine.IsEmpty() then
            Error(PackageAlreadyReadErr);



        PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.SetRange("Package No.", PackageTransferHeader.Barcode);
        PackageNoInformation.FindFirst();

        if PackageNoInformation.Blocked then
            Error(PackageBlockedErr);
        PackageNoInformation.CalcFields("Line Item No. IPK");
        if InventoryAdjustment.IsItemInAnyActiveInventoryAdjustment(PackageNoInformation."Line Item No. IPK") and (PackageTransferHeader."Package Transfer Type" <> PackageTransferHeader."Package Transfer Type"::"Inventory Adjustment") then
            Error(PackageInventoryAdjustmentLbl, PackageNoInfoLine."Package No.");
        PackageNoInfoLine.SetAutoCalcFields("Location Code");
        PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
        PackageNoInfoLine.FindSet();

        if PackageTransferHeader."Transfer-from Code" = '' then begin

            // Location.Get(PackageNoInfoLine."Location Code");
            // Location.TestField("Production Location IPK", false);

            PackageTransferHeader.Validate("Transfer-from Code", PackageNoInfoLine."Location Code");
            PackageTransferHeader.Modify(true);
        end
        else
            PackageTransferHeader.TestField("Transfer-from Code", PackageNoInfoLine."Location Code");

        QualityControlHeaderQCM.SetRange("Package No.", PackageNoInfoLine."Package No.");



        if not QualityControlHeaderQCM.FindFirst() then begin
            QualityControlHeaderQCM.Reset();
            QualityControlHeaderQCM.SetRange("Lot No.", PackageNoInfoLine."Lot No.");
            QualityControlHeaderQCM.SetRange("Item No.", PackageNoInfoLine."Line Item No.");
            if QualityControlHeaderQCM.FindFirst() then;//
        end;
        CheckAvailibilityForQualitycontrol(PackageTransferHeader, PackageNoInfoLine, QualityControlHeaderQCM);

        repeat
            CreatePackageTransferLineFromPackageNoInfoLine(PackageTransferHeader, PackageNoInfoLine);
        until PackageNoInfoLine.Next() = 0;
        // CreatePackageTransferLine(PackageTransferHeader, PackageNoInformation);

        PackageTransferHeader.Barcode := '';
    end;

    // procedure ShipAndRecievePackagetransferHeader(var PackageTransferHeader: Record "Package Transfer Header IPK")
    // var
    //     PackageTransferLine: Record "Package Transfer Line IPK";
    //     PackageNoInfoLine: Record "Package No. Info. Line IPK";
    //     AtleastErr: Label 'You have to transfer at least one units of: %1', Comment = '%1="Item Journal Line"."Item No."';
    //     IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
    // begin
    //     PackageTransferHeader.Validate(Shipped, true);
    //     PackageTransferHeader.Modify(true);
    //     PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
    //     PackageTransferLine.FindSet();
    //     repeat
    //         if not (PackageTransferLine."Quantity To Transfer" > 0) then
    //             Error(AtleastErr, PackageTransferLine."Item No.");



    //         PackageNoInfoLine.SetRange("Package No.", PackageTransferLine."Package No.");
    //         PackageNoInfoLine.FindSet();
    //         IpekPackageTransMgt.CheckAvailibilityForQualitycontrol(PackageTransferHeader, PackageNoInfoLine);

    //     until PackageTransferLine.Next() = 0;





    //     CreateAndPostItemReclassificationJournal(PackageTransferHeader, false);
    // end;

    procedure ShipAndRecievePackagetransferHeader(var PackageTransferHeader: Record "Package Transfer Header IPK"; var QualityControlHeaderQCM: Record "Quality Control Header QCM")
    var
        // PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageTransferLine: Record "Package Transfer Line IPK";
        // IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
        AtleastErr: Label 'You have to transfer at least one units of: %1', Comment = '%1="Item Journal Line"."Item No."';
    begin
        PackageTransferHeader.Validate(Shipped, true);
        PackageTransferHeader.Modify(true);
        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.FindSet();
        repeat
            if not (PackageTransferLine."Quantity To Transfer" > 0) then
                Error(AtleastErr, PackageTransferLine."Item No.");



        // PackageNoInfoLine.SetRange("Package No.", PackageTransferLine."Package No.");
        // PackageNoInfoLine.FindSet();
        // if not QualityControlHeaderQCM.IsEmpty() then
        //     IpekPackageTransMgt.CheckAvailibilityForQualitycontrol(PackageTransferHeader, PackageNoInfoLine, QualityControlHeaderQCM);

        until PackageTransferLine.Next() = 0;





        CreateAndPostItemReclassificationJournal(PackageTransferHeader, false);
    end;

    local procedure CreatePackageTransferLineFromPackageNoInfoLine(var PackageTransferHeader: Record "Package Transfer Header IPK"; var PackageNoInfoLine: Record "Package No. Info. Line IPK")
    var
        PackageNoInfoLine2: Record "Package No. Info. Line IPK";
        PackageTransferLine: Record "Package Transfer Line IPK";
    begin
        PackageTransferLine.Init();
        PackageTransferLine."Document No." := PackageTransferHeader."No.";
        PackageTransferLine.Insert(true);
        PackageTransferLine.Validate("Item No.", PackageNoInfoLine."Line Item No.");
        PackageTransferLine.Validate("Variant Code", PackageNoInfoLine."Line Variant Code");
        PackageTransferLine.Validate(Quantity, PackageNoInfoLine.Quantity);
        // PackageTransferLine.Validate("Quantity At Creation", PackageNoInfoLine.Quantity);

        PackageNoInfoLine2.SetRange("Source Package No. IPK", PackageNoInfoLine."Package No.");


        if (PackageTransferHeader."Is Production Location") and (PackageNoInfoLine2.IsEmpty()) then
            PackageTransferLine.Validate("Quantity To Transfer", 0)
        else
            PackageTransferLine.Validate("Quantity To Transfer", PackageNoInfoLine.Quantity);

        //8) Package Transfer Order satırlarında "Quantity to Transfer" alanı boş gelsin,
        // ve Post edildiğinde herhangi bir satırın quantity to transfer alanı sıfır ise sistem hata versin ve transferi iptal etsin.
        PackageTransferLine.Validate(Description, PackageNoInfoLine.Description);
        PackageTransferLine.Validate("Lot No.", PackageNoInfoLine."Lot No.");
        PackageTransferLine.Validate("Package No.", PackageNoInfoLine."Package No.");
        PackageTransferLine.Modify(true);
    end;

    local procedure GetLineNo(var TransferHeader: Record "Transfer Header"; var TransferLine: Record "Transfer Line")
    var
        TransLine2: Record "Transfer Line";
    begin
        TransLine2.Reset();
        TransLine2.SetFilter("Document No.", TransferHeader."No.");
        if TransLine2.FindLast() then
            TransferLine."Line No." := TransLine2."Line No." + 10000
        else
            TransferLine."Line No." := 10000;
    end;

    procedure CheckAvailibilityForQualitycontrol(var PackageTransferHeader: Record "Package Transfer Header IPK"; var PackageNoInfoLine: Record "Package No. Info. Line IPK"; QualityControlHeaderQCM: Record "Quality Control Header QCM")
    var
        LocationQualityCtrlSetup: Record "Location - Quality Ctrl. Setup";
    // TransferPackageQualityControlErr: Label 'You cannot transfer this package to: %1 according to Quality Control Document: %2 , Quality Control- Location Setup. ', Comment = '%1="Package Transfer Header IPK"."Transfer-to Code",%2=QcHeader';
    begin
        // if IsHandled then
        //     exit;
        // QualityControlHeaderQCM.SetRange("Package No.", PackageNoInfoLine."Package No.");

        // if not QualityControlHeaderQCM.FindFirst() then begin
        //     QualityControlHeaderQCM.Reset();
        //     QualityControlHeaderQCM.SetRange("Lot No.", PackageNoInfoLine."Lot No.");
        //     QualityControlHeaderQCM.SetRange("Item No.", PackageNoInfoLine."Line Item No.");
        //     if QualityControlHeaderQCM.FindFirst() then;//TOdo:Confirm Question about qc doc not found
        //                                                 // else

        // end;

        LocationQualityCtrlSetup.SetRange("Quality Control Type", QualityControlHeaderQCM.Type);//Son kalan yer
        LocationQualityCtrlSetup.SetRange("Quality Control Status", QualityControlHeaderQCM.Status);
        LocationQualityCtrlSetup.SetRange("Allowed Location Code", PackageTransferHeader."Transfer-to Code");

        // if (QualityControlHeaderQCM."No." = '') then
        //     Error('Cannot find the quality control document for this package.');


        //Quality Control Location check!!!!
        // if (LocationQualityCtrlSetup.IsEmpty()) and (QualityControlHeaderQCM."No." <> '') then
        //     Error(TransferPackageQualityControlErr, PackageTransferHeader."Transfer-to Code", QualityControlHeaderQCM."No.");
    end;

    procedure CreateNewTransferLineFromTransferOrder(var TransferHeader: Record "Transfer Header"; var PackageTransferLine: Record "Package Transfer Line IPK")
    var
        TransferLine: Record "Transfer Line";
    begin

        TransferLine.Init();

        TransferLine."Document No." := TransferHeader."No.";

        GetLineNo(TransferHeader, TransferLine);

        TransferLine.Insert(true);


        TransferLine.Validate("Item No.", PackageTransferLine."Item No.");
        TransferLine.Validate("Variant Code", PackageTransferLine."Variant Code");
        TransferLine.Validate(Quantity, PackageTransferLine.Quantity);
        TransferLine.Validate(Description, PackageTransferLine.Description);
        TransferLine.Modify(true);
        AssignItemTrackingNoInformationToTransferLine(TransferLine, PackageTransferLine);

    end;

    procedure CreateAndPostTransferOrder(var PackageTransferHeader: Record "Package Transfer Header IPK")
    var
        PackageTransferLine: Record "Package Transfer Line IPK";
        TransferHeader: Record "Transfer Header";
    begin
        PackageTransferHeader.TestField(Received, false);
        TransferHeader.Init();

        TransferHeader.Insert(true);
        TransferHeader.Validate("Transfer-from Code", PackageTransferHeader."Transfer-from Code");
        TransferHeader.Validate("Posting Date", PackageTransferHeader."Posting Date");
        TransferHeader.Validate("Transfer-to Code", PackageTransferHeader."Transfer-to Code");
        // TransferHeader.Validate("Direct Transfer", true);
        // Location.SetRange();
        TransferHeader.Validate("In-Transit Code", 'NAKLIYE');
        TransferHeader.Modify(true);

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.FindSet();
        repeat
            CreateNewTransferLineFromTransferOrder(TransferHeader, PackageTransferLine);
        until PackageTransferLine.Next() = 0;

        // Codeunit.Run(Codeunit::"TransferOrder-Post (Yes/No)", TransferHeader);
        // PackageTransferHeader.Validate(Posted, true);
        // PackageTransferHeader.Modify(true);
    end;

    procedure CreateAndPostItemReclassificationJournal(var PackageTransferHeader: Record "Package Transfer Header IPK"; HideDialog: Boolean)
    var
        ItemJournalLine: Record "Item Journal Line";
        // WarehouseReceiptLine: Record "Warehouse Receipt Line";
        LastItemJournalLine: Record "Item Journal Line";
        PackageTransferLine: Record "Package Transfer Line IPK";
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl IPK";
        InventoryAdjustment: Codeunit "Inventory Adjustment IPK";
        LineNo: Integer;
        LocationSameErr: Label 'Transfer-from and transfer-to must be different.';
        PackSuccessMsg: Label 'Package Transfer Order: %1 Succesfully Received', Comment = '%1="Package Transfer Header IPK"."No."';
        ItemBlockedInventoryAdjLbl: Label '%1 is currenly blocked for Inventory Adjustment', Comment = '%1="Package Transfer Line IPK"."Item No."';
    begin
        IpekPamukSetup.GetRecordOnce();
        IpekPamukSetup.TestField("Package Tran. Jnl. Batch Name");
        IpekPamukSetup.TestField("Package Tran. Jnl. Temp. Name");

        PackageTransferHeader.TestField("Transfer-to Code");
        PackageTransferHeader.TestField("Transfer-from Code");

        if PackageTransferHeader."Transfer-to Code" = PackageTransferHeader."Transfer-from Code" then
            Error(LocationSameErr);
        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.FindSet();

        LastItemJournalLine.SetRange("Journal Template Name", IpekPamukSetup."Package Tran. Jnl. Temp. Name");
        LastItemJournalLine.SetRange("Journal Batch Name", IpekPamukSetup."Package Tran. Jnl. Batch Name");

        if LastItemJournalLine.FindLast() then
            LineNo := LastItemJournalLine."Line No." + 10000
        else
            LineNo := 10000;

        repeat

            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := IpekPamukSetup."Package Tran. Jnl. Temp. Name";
            ItemJournalLine."Journal Batch Name" := IpekPamukSetup."Package Tran. Jnl. Batch Name";
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine."Entry Type" := ItemJournalLine."Entry Type"::Transfer;
            ItemJournalLine."Line No." := LineNo;
            ItemJournalLine.Insert(true);


            if (PackageTransferHeader."Package Transfer Type" <> PackageTransferHeader."Package Transfer Type"::"Inventory Adjustment") and InventoryAdjustment.ExistsActiveInventoryAdjustmentForItem(PackageTransferLine."Item No.") then
                Error(ItemBlockedInventoryAdjLbl, PackageTransferLine."Item No.");


            ItemJournalLine.Validate("Item No.", PackageTransferLine."Item No.");
            ItemJournalLine.Validate("Variant Code", PackageTransferLine."Variant Code");
            ItemJournalLine.Validate("Location Code", PackageTransferHeader."Transfer-from Code");
            ItemJournalLine.Validate("New Location Code", PackageTransferHeader."Transfer-to Code");

            ItemJournalLine.Validate(Quantity, PackageTransferLine."Quantity To Transfer");
            ItemJournalLine.Modify(true);
            IpekProductionManagement.AssignLotNoToItemJournalLine(ItemJournalLine, PackageTransferLine."Lot No.", ItemJournalLine."Quantity (Base)", ItemJournalLine.Quantity, PackageTransferLine."Lot No.");
            LineNo += 10000;
            LastItemJournalLine := ItemJournalLine;
            UpdatePackNoInformation(PackageTransferLine, PackageTransferHeader);
        until PackageTransferLine.Next() = 0;

        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        PackageTransferHeader.Validate(Shipped, true);
        PackageTransferHeader.Validate(Received, true);
        PackageTransferHeader.Modify(true);


        repeat

            WarehouseReceiptLineDtl.SetRange("Package No.", PackageTransferLine."Package No.");
            WarehouseReceiptLineDtl.SetRange("Item No.", PackageTransferLine."Item No.");
            WarehouseReceiptLineDtl.SetRange("Lot No.", PackageTransferLine."Lot No.");
            WarehouseReceiptLineDtl.ModifyAll(Recieved, true, true);

        until PackageTransferLine.Next() = 0;

        if not HideDialog then
            Message(PackSuccessMsg, PackageTransferHeader."No.");
        // Page.Run(Page::"Item Reclass. Journal", ItemJournalLine);
    end;

    [EventSubscriber(ObjectType::Page, Page::"Items by Location", OnAfterSetTempMatrixLocationFilters, '', false, false)]
    local procedure "Items by Location_OnAfterSetTempMatrixLocationFilters"(var Sender: Page "Items by Location"; var TempMatrixLocation: Record Location temporary)
    begin
        TempMatrixLocation.SetRange("Visible On Items By LocMat IPK", true);
    end;

    procedure UpdatePackNoInformation(var PackageTransferLine: Record "Package Transfer Line IPK"; var PackageTransferHeader: Record "Package Transfer Header IPK")
    var
        LivePackageTransferLine: Record "Package No. Info. Line IPK";
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
        PalletNoInformation: Record "Package No. Information";
    begin
        PackageNoInformation.Get('', '', PackageTransferLine."Package No.");

        if PackageTransferLine.Quantity <> PackageTransferLine."Quantity To Transfer" then begin
            PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
            PackageNoInfoLine.SetRange("Line Item No.", PackageTransferLine."Item No.");
            PackageNoInfoLine.SetRange("Lot No.", PackageTransferLine."Lot No.");
            PackageNoInfoLine.FindFirst();


            LivePackageTransferLine.SetRange("Package No.", PackageTransferLine."Package No.");
            LivePackageTransferLine.SetRange("Lot No.", PackageTransferLine."Lot No.");
            LivePackageTransferLine.SetRange("Line Item No.", PackageTransferLine."Item No.");
            LivePackageTransferLine.SetRange("Line Variant Code", PackageTransferLine."Variant Code");
            LivePackageTransferLine.FindFirst();
            // LiveQuantity := ;


            PackageNoInfoLine.Validate(Quantity, LivePackageTransferLine.Quantity - PackageTransferLine."Quantity To Transfer");
            PackageNoInfoLine.Modify(true);
        end
        else begin
            PackageNoInformation."Location Code IPK" := PackageTransferHeader."Transfer-to Code";
            PackageNoInformation.Modify(true);

            PackageNoInfoLine.SetRange("Source Package No. IPK", PackageTransferLine."Package No.");
            if PackageNoInfoLine.FindFirst() then
                if PalletNoInformation.Get('', '', PackageNoInfoLine."Package No.") then begin
                    PalletNoInformation."Location Code IPK" := PackageTransferHeader."Transfer-to Code";
                    PalletNoInformation.Modify(true);
                end
        end;

    end;

    procedure AssignItemTrackingNoInformationToTransferLine(TransferLine: Record "Transfer Line"; PackageTransferLine: Record "Package Transfer Line IPK")
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        TransferLineReserve: Codeunit "Transfer Line-Reserve";
        // TransferHeader: Record "Transfer Header";
        ItemTrackingLines: Page "Item Tracking Lines";
        AvailabilityDate: Date;
    begin
        // TransferHeader.Get(TransferLine."Document No.");
        AvailabilityDate := WorkDate();
        // TransferLineReserve.InitFromTransLine(SourceTrackingSpecification, TransferLine, AvailabilityDate, Enum::"Transfer Direction"::Outbound);
        // if TransferHeader."Direct Transfer" then
        //     ItemTrackingLines.SetDirectTransfer(true);
        // ItemTrackingLines.SetSourceSpec(SourceTrackingSpecification, AvailabilityDate);
        // ItemTrackingLines.SetInbound(TransferLine.IsInbound());
        //


        TransferLineReserve.InitFromTransLine(TempSourceTrackingSpecification, TransferLine, AvailabilityDate, Enum::"Transfer Direction"::Outbound);
        // TransferLine.Quantity:=PackageTransferLine.;
        // TransferLine."Quantity (Base)";
        // TransferLine."Quantity Received";

        // TempTrackingSpecification."Lot No." := PackageTransferLine."Lot No.";//LotNo;
        // TempTrackingSpecification."Package No." := PackageTransferLine."Package No.";// PackageNo;
        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := PackageTransferLine."Lot No.";
        // TempTrackingSpecification."Package No." := PackageTransferLine."Package No.";
        TempTrackingSpecification.SetQuantities(TransferLine."Qty. to Ship (Base)",
                                                TransferLine."Qty. to Ship",
                                                TransferLine."Qty. to Ship (Base)",
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);
        // TempSourceTrackingSpecification := TempTrackingSpecification;
        // ItemTrackingLines.SetBlockCommit(true);

        //

        ItemTrackingLines.SetBlockCommit(true);
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AvailabilityDate, TempTrackingSpecification);
    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCopyTrackingSpec, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCopyTrackingSpec"(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    begin
        if OverrideQuantityHandled then
            DestTrkgSpec."Quantity Handled (Base)" := SourceTrackingSpec."Quantity Handled (Base)";

    end;

    // local procedure AssignItemTrackingInformationToTransferLine(LotNo: Code[50]; PackageNo: Code[50]; var TransferLine: Record "Transfer Line"; AvailabilityDate: Date; QtyToReceiveBase: Decimal; QtyToReceive: Decimal/*; QtyReceivedBase: Decimal*/)
    // var
    //     TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
    //     TempTrackingSpecification: Record "Tracking Specification" temporary;
    //     TransferLineReserve: Codeunit "Transfer Line-Reserve";
    //     // SecondSourceQtyArray: array[3] of Decimal;
    //     ItemTrackingLines: Page "Item Tracking Lines";

    // begin
    //     // TransferLineReserve.InitFromTransLine(TempSourceTrackingSpecification, TransferLine);

    //     // SecondSourceQtyArray[1] := Database::"Warehouse Receipt Line";
    //     // SecondSourceQtyArray[2] := QtyToReceiveBase;
    //     // SecondSourceQtyArray[3] := 0;
    //     // SkipOpenItemTrackingLinePage := true;
    //     // // PurchLineReserve.CallItemTracking(PurchaseLine, SecondSourceQtyArray);
    //     // PurchLineReserve.InitFromPurchLine(SourceTrackingSpecification, PurchaseLine);
    //     // ItemTrackingLines.SetSourceSpec(SourceTrackingSpecification, PurchaseLine."Expected Receipt Date");
    //     // ItemTrackingLines.SetSecondSourceQuantity(SecondSourceQtyArray);

    //     // SkipOpenItemTrackingLinePage := false;

    //     TempTrackingSpecification.Init();
    //     TempTrackingSpecification."Lot No." := LotNo;

    //     TempTrackingSpecification."Package No." := PackageNo;

    //     TempTrackingSpecification.SetQuantities(QtyToReceiveBase,
    //                                             QtyToReceive,
    //                                             QtyToReceiveBase,
    //                                             0,
    //                                             0,
    //                                             0,
    //                                             0);
    //     TempTrackingSpecification.Insert(false);
    //     //SourceTrackingSpecification := TempTrackingSpecification;

    //     //SourceTrackingSpecification.Description := '';
    //     // SourceTrackingSpecification."Source Type" := Database::"Purchase Line";
    //     ItemTrackingLines.SetBlockCommit(true);
    //     OverrideQuantityHandled := true;
    //     ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AvailabilityDate, TempTrackingSpecification);
    //     OverrideQuantityHandled := false;
    // end;

    procedure FixPackagesCurrentLocationCode()
    var
        PackageNoInformation: Record "Package No. Information";
        PackageTransferHeader: Record "Package Transfer Header IPK";
        PackageTransferLine: Record "Package Transfer Line IPK";
    begin
        PackageTransferHeader.SetRange(Received, true);
        PackageTransferHeader.FindSet();
        repeat
            PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
#pragma warning disable LC0082
            if PackageTransferLine.Count() > 1 then begin
#pragma warning restore LC0082
                PackageTransferLine.FindSet();
                repeat
                    if PackageTransferLine.Quantity = PackageTransferLine."Quantity To Transfer" then begin
                        PackageNoInformation.Get('', '', PackageTransferLine."Package No.");
                        PackageNoInformation.Validate("Location Code IPK", PackageTransferHeader."Transfer-to Code");
                        PackageNoInformation.Modify(true);
                    end;
                until PackageTransferLine.Next() = 0;
            end;
        until PackageTransferHeader.Next() = 0;

        Message('Packages Current Location Code Fixed');
    end;

    procedure CreatePackageTransferDocumentFromPackageCombineHeader(var PackageCombineHeader: Record "Package Combine Header IPK"): Code[20]
    var
        PackageTransferHeader: Record "Package Transfer Header IPK";

    begin
        PackageTransferHeader.Init();
        PackageTransferHeader.Insert(true);
        PackageTransferHeader.Validate("Transfer-to Code", PackageCombineHeader."Target Location");
        PackageTransferHeader.Validate("Source Document No.", PackageCombineHeader."No.");
        PackageTransferHeader.Modify(true);

        PackageTransferHeader.Validate(Barcode, PackageCombineHeader."New Package No.");
        PackageTransferHeader.Validate("Package Transfer Type", PackageTransferHeader."Package Transfer Type"::"Package Combine");
        PackageTransferHeader.Modify(true);
        exit(PackageTransferHeader."No.");

    end;
    #region PackageCombine
    procedure PackageCombinePopulateTable(var Rec: Record "Package Combine Header IPK")
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        PackageCombineLine: Record "Package Combine Line IPK";
        PackageQuantityToCombine: Decimal;
    begin
        PackageCombineLine.SetRange("Document No.", Rec."No.");
        PackageCombineLine.DeleteAll(true);

        PackageQuantityToCombine := Rec."New Package Quantity";
        ItemLedgerEntry.SetCurrentKey("Lot No.");
        ItemLedgerEntry.SetRange("Item No.", Rec."Item No. Filter");
        ItemLedgerEntry.SetRange(Open, true);
        ItemLedgerEntry.SetRange("Variant Code", Rec."Variant Code Filter");
        ItemLedgerEntry.SetRange("Location Code", Rec."Location Code Filter");
        // ItemLedgerEntry.SetRange(Open, true);

        ItemLedgerEntry.FindSet();
        repeat
            PackageCombineLine.SetRange("Document No.", Rec."No.");
            PackageCombineLine.SetRange("Lot No.", ItemLedgerEntry."Lot No.");
            PackageCombineLine.SetRange("Item No.", ItemLedgerEntry."Item No.");
            PackageCombineLine.SetRange("Variant Code", ItemLedgerEntry."Variant Code");
            PackageCombineLine.SetRange("Location Code", ItemLedgerEntry."Location Code");

            if PackageCombineLine.FindFirst() then
                PackageCombineLine.Quantity += ItemLedgerEntry."Remaining Quantity"
            else begin
                PackageCombineLine.Init();
                PackageCombineLine."Document No." := Rec."No.";
                PackageCombineLine.Insert(true);

                PackageCombineLine.Validate("Item No.", ItemLedgerEntry."Item No.");
                PackageCombineLine.Validate("Variant Code", ItemLedgerEntry."Variant Code");
                PackageCombineLine.Validate("Location Code", ItemLedgerEntry."Location Code");
                PackageCombineLine.Validate("Lot No.", ItemLedgerEntry."Lot No.");

                PackageCombineLine.Validate(Description, ItemLedgerEntry.Description);
                PackageCombineLine.Quantity := ItemLedgerEntry."Remaining Quantity";
            end;

            if PackageQuantityToCombine > 0 then
                if PackageQuantityToCombine > ItemLedgerEntry."Remaining Quantity" then begin
                    PackageCombineLine."Quantity-to Add" += ItemLedgerEntry."Remaining Quantity";
                    PackageQuantityToCombine -= ItemLedgerEntry."Remaining Quantity";
                end
                else begin
                    PackageCombineLine."Quantity-to Add" += PackageQuantityToCombine;
                    PackageQuantityToCombine -= PackageQuantityToCombine;
                end;



            PackageCombineLine.Modify(true);
        until ItemLedgerEntry.Next() = 0;
    end;

    procedure PackageCombineShip(var Rec: Record "Package Combine Header IPK")
    var
        InventorySetup: Record "Inventory Setup";
        PackageCombineLine: Record "Package Combine Line IPK";
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
        PackageNoInformation: Record "Package No. Information";
        NoSeries: Codeunit "No. Series";
    begin
        Rec.CalcFields("Total Quantity-to Add");
        Rec.TestField("New Package Quantity", Rec."Total Quantity-to Add");

        InventorySetup.GetRecordOnce();
        InventorySetup.TestField("Package Nos.");
        Rec."New Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.");

        PackageNoInformation.Init();
        PackageNoInformation."Item No." := '';
        PackageNoInformation."Variant Code" := '';
        PackageNoInformation."Package No." := Rec."New Package No.";
        PackageNoInformation.Insert(true);
        PackageNoInformation.Validate("Location Code IPK", Rec."Location Code Filter");
        PackageNoInformation.Validate("Document No. IPK", Rec."No.");
        PackageNoInformation.Validate("Created At IPK", Rec."Operation Date-Time");
        PackageNoInformation.Modify(true);

        PackageCombineLine.SetRange("Document No.", Rec."No.");
        PackageCombineLine.SetFilter("Quantity-to Add", '>0');
        PackageCombineLine.FindSet();
        repeat
            PackageNoInfoLine.Init();
            PackageNoInfoLine."Item No." := '';
            PackageNoInfoLine."Variant Code" := '';
            PackageNoInfoLine."Package No." := PackageNoInformation."Package No.";
            PackageNoInfoLine.Insert(true);
            PackageNoInfoLine.Validate("Line Item No.", PackageCombineLine."Item No.");
            PackageNoInfoLine.Validate("Line Variant Code", PackageCombineLine."Variant Code");
            PackageNoInfoLine.Validate(Quantity, PackageCombineLine."Quantity-to Add");
            PackageNoInfoLine.Validate("Lot No.", PackageCombineLine."Lot No.");
            PackageNoInfoLine.Modify(true);

        until PackageCombineLine.Next() = 0;
        Rec.Shipped := true;
        Commit();//
        PackageNoInformation.SetRecFilter();
        Report.Run(Report::"Palette Label IPK", true, true, PackageNoInformation);
    end;

    procedure PackageCombineCombine(var Rec: Record "Package Combine Header IPK")
    var
        NewPackageNoInfoLineBase: Record "Package No. Info. Line IPK";
        PackageNoInfoLineBase: Record "Package No. Info. Line IPK";
        PackageNoInfoLineToCombine: Record "Package No. Info. Line IPK";
        PackageNoInformationToCombine: Record "Package No. Information";
    begin
        PackageNoInfoLineBase.SetRange("Package No.", Rec."Target Package No.");
        PackageNoInfoLineBase.SetRange("Line Item No.", Rec."Item No. Filter");
        PackageNoInfoLineBase.SetRange("Line Variant Code", Rec."Variant Code Filter");

        PackageNoInfoLineToCombine.SetRange("Package No.", Rec."New Package No.");
        PackageNoInfoLineToCombine.SetRange("Line Item No.", Rec."Item No. Filter");
        PackageNoInfoLineToCombine.SetRange("Line Variant Code", Rec."Variant Code Filter");
        PackageNoInfoLineToCombine.FindSet();

        repeat

            PackageNoInfoLineBase.SetRange("Lot No.", PackageNoInfoLineToCombine."Lot No.");
            if PackageNoInfoLineBase.FindFirst() then begin
                PackageNoInfoLineBase.Quantity += PackageNoInfoLineToCombine.Quantity;
                PackageNoInfoLineBase.Modify(true);
            end
            else begin
                NewPackageNoInfoLineBase.Init();
                NewPackageNoInfoLineBase."Item No." := '';
                NewPackageNoInfoLineBase."Variant Code" := '';
                NewPackageNoInfoLineBase."Package No." := Rec."Target Package No.";
                NewPackageNoInfoLineBase.Insert(true);
                NewPackageNoInfoLineBase.Validate(Quantity, PackageNoInfoLineToCombine.Quantity);
                NewPackageNoInfoLineBase.Validate("Line Variant Code", PackageNoInfoLineToCombine."Line Variant Code");
                NewPackageNoInfoLineBase.Validate("Line Item No.", PackageNoInfoLineToCombine."Line Item No.");
                NewPackageNoInfoLineBase.Validate("Lot No.", PackageNoInfoLineToCombine."Lot No.");
                NewPackageNoInfoLineBase.Modify(true);
            end;
        //PackageNoInfoLineToCombine.Quantity := 0;

        until PackageNoInfoLineToCombine.Next() = 0;

        PackageNoInformationToCombine.Get('', '', PackageNoInfoLineToCombine."Package No.");
        PackageNoInformationToCombine.Blocked := true;
        PackageNoInformationToCombine."New Package No. IPK" := Rec."Target Package No.";
        PackageNoInformationToCombine.Modify(true);
        Rec.Combined := true;
    end;
    #endregion
    procedure CheckPalletIsMixed(var PackageNoInfoLine: Record "Package No. Info. Line IPK")
    var
        PackageNoInformation: Record "Package No. Information";
        CannotMixPackageErr: Label 'You cannot mix packages with items.';
    begin
        if PackageNoInformation.Get('', '', PackageNoInfoLine."Package No.") then begin

            PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");

            if PackageNoInformation."Pallet IPK" then begin
                if PackageNoInfoLine."Source Package No. IPK" = '' then
                    Error(CannotMixPackageErr);
            end
            else
                if PackageNoInfoLine."Source Package No. IPK" <> '' then
                    Error(CannotMixPackageErr);

        end;
    end;



    var

        // IpekBasicFunctions: Codeunit "Ipek Basic Functions IPK";
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        IpekProductionManagement: Codeunit "Ipek Production Management IPK";
        OverrideQuantityHandled: Boolean;
}