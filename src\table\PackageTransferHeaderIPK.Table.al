table 60014 "Package Transfer Header IPK"
{
    DataClassification = CustomerContent;
    Caption = 'Package Transfer Header';
    DrillDownPageId = "Package Transfer Orders IPK";
    LookupPageId = "Package Transfer Orders IPK";
    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            Editable = false;
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                IpekSetup: Record "Ipek Pamuk Setup IPK";
                NoSeriesManagement: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    IpekSetup.Get();
                    NoSeriesManagement.TestManual(IpekSetup."Package Transfer No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Transfer-from Code"; Code[10])
        {
            Caption = 'Transfer-from Code';
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
            TableRelation = Location where("Use As In-Transit" = const(false));
        }
        // field(3; "Transfer-from Bin Code"; Code[20])
        // {
        //     Caption = 'Transfer-from Bin Code';
        //     TableRelation = Bin.Code where("Location Code" = field("Transfer-from Code"));
        // }
        field(4; "Transfer-to Code"; Code[10])
        {
            Caption = 'Transfer-to Code';
            TableRelation = Location where("Use As In-Transit" = const(false));
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
        }
        // field(5; "Transfer-To Bin Code"; Code[20])
        // {
        //     Caption = 'Transfer-To Bin Code';
        //     TableRelation = Bin.Code where("Location Code" = field("Transfer-to Code"));
        //     ToolTip = 'Specifies the value of the Transfer-To Bin Code field.';
        // }
        field(6; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(7; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            var
                PackageNoInfoLine: Record "Package No. Info. Line IPK";
                PackageNoInformation: Record "Package No. Information";
            begin
                if PackageNoInformation.Get('', '', Rec.Barcode) and PackageNoInformation."Pallet IPK" then begin
                    PackageNoInfoLine.SetRange("Item No.", '');
                    PackageNoInfoLine.SetRange("Variant Code", '');
                    PackageNoInfoLine.SetRange("Package No.", PackageNoInformation."Package No.");
                    PackageNoInfoLine.FindSet();


                    repeat
                        Rec.Barcode := PackageNoInfoLine."Source Package No. IPK";
                        IpekPackageTransMgt.ProcessBarcode(Rec);
                    until PackageNoInfoLine.Next() = 0;

                end
                else
                    IpekPackageTransMgt.ProcessBarcode(Rec);

            end;
        }
        field(8; Received; Boolean)
        {
            Caption = 'Received';
            ToolTip = 'Specifies the value of the Received field.';
            // trigger OnValidate()
            // var

            // begin
            //     // Message(PackSuccessMsg, Rec."No.");
            // end;
        }
        // field(9; "Production Order No."; Code[20])
        // {
        //     Caption = 'Production Order No.';
        //     TableRelation = "Production Order"."No." where(Status = const(Released));
        //     ToolTip = 'Specifies the value of the Production Order No. field.';
        // }
        // field(10; "Transferring to Prod. Location"; Boolean)
        // {
        //     Caption = 'Transferring to Prod. Location';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup(Location."Production Location IPK" where(Code = field("Transfer-to Code")));
        //     ToolTip = 'Specifies the value of the Transferring to Prod. Location field.';
        // }
        field(11; "Total Transfer Quantity"; Decimal)
        {
            Caption = 'Total Transfer Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package Transfer Line IPK".Quantity where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Transfer Quantity field.';
        }

        // field(12; "Location Filter"; Code[250])
        // {
        //     Caption = 'Location Filter';
        //     ToolTip = 'Specifies the value of the Location Filter field.';
        // }


        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
        field(12; Shipped; Boolean)
        {
            Caption = 'Shipped';
            ToolTip = 'Specifies the value of the Shipped field.';
        }
        field(13; "Is Production Location"; Boolean)
        {
            Caption = 'Is Production Location';
            ToolTip = 'Specifies the value of the Is Production Location field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Production Location IPK" where(Code = field("Transfer-to Code")));
        }
        field(14; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            Editable = false;
            DataClassification = ToBeClassified;
            AllowInCustomizations = Always;
        }
        field(3; "Palette Count"; Integer)
        {
            Caption = 'Palette Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package Transfer Line IPK" where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Palette Count field.';
        }
        field(15; "Package Transfer Type"; Enum "Package Transfer Type IPK")
        {
            Caption = 'Package Transfer Type';
            Editable = false;
            DataClassification = ToBeClassified;
            AllowInCustomizations = Always;
            ToolTip = 'Executes the Assign Package Transfer Type action.';
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(key2; "Source Document No.", Shipped, Received)
        {

        }
    }

    trigger OnInsert()
    var
        IpekSetup: Record "Ipek Pamuk Setup IPK";
        NoSeriesManagement: Codeunit "No. Series";
    begin
        // if "No." = '' then begin
        //     IpekSetup.Get();
        //     IpekSetup.TestField("Package Transfer No. Series");
        //     NoSeriesManagement.InitSeries(IpekSetup."Package Transfer No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            IpekSetup.Get();
            IpekSetup.TestField("Package Transfer No. Series");
            "No. Series" := IpekSetup."Package Transfer No. Series";
            if NoSeriesManagement.AreRelated(IpekSetup."Package Transfer No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

        "Posting Date" := WorkDate();

        // UserSetup.Get(UserId);

        // Rec."Location Filter" := UserSetup."Location Filter IPK";
    end;



    trigger OnDelete()
    var
        PackageTransferLine: Record "Package Transfer Line IPK";
    begin
        Rec.TestField(Received, false);
        PackageTransferLine.SetRange("Document No.", Rec."No.");
        PackageTransferLine.DeleteAll(true);
    end;

    var
        IpekPackageTransMgt: Codeunit "Ipek Package Trans. Mgt. IPK";
}