codeunit 60000 "Ipek Production Management IPK"
{
    Access = Internal;
    SingleInstance = true;

    procedure GetNextLotNoFromItemNo(ItemNo: Code[20]): Code[50]
    var
        Item: Record Item;
    //NoSeries: Codeunit "No. Series";
    begin
        if not Item.Get(ItemNo) then
            exit('');

        if Item."Lot Nos." = '' then
            exit('');

        exit(NoSeries.GetNextNo(Item."Lot Nos."));
    end;

    procedure UpdateLastProducedDateTime(var ProductionOrder: Record "Production Order"; ParDateTime: DateTime)
    begin
        ProductionOrder.Validate("Last Production Date-Time IPK", ParDateTime);
        ProductionOrder.Modify(true);
    end;

    procedure UpdateHistoricLast()
    var
        ProdOrderLineDetail: Record "Prod. Order Line Detail IPK";
        ProductionOrder: Record "Production Order";
        ProductionDateUpdateMessageLbl: Label 'All Last Produced Dates are updated!';
    begin
        ProductionOrder.FindSet(true);
        repeat

            ProdOrderLineDetail.SetCurrentKey(SystemCreatedAt);
            ProdOrderLineDetail.SetRange("Prod. Order No.", ProductionOrder."No.");
            if ProdOrderLineDetail.FindLast() then begin
                ProductionOrder."Last Production Date-Time IPK" := ProdOrderLineDetail.SystemCreatedAt;
                ProductionOrder.Modify(false);
            end;
        until ProductionOrder.Next() = 0;

        Message(ProductionDateUpdateMessageLbl);
    end;

    procedure CreateProductionOrderLineDetailsFromPackageCreation(var TempPackageCreation: Record "Package Creation IPK" temporary): Code[50]
    var
        InventorySetup: Record "Inventory Setup";
        PackageNoInformation: Record "Package No. Information";
        ProdOrderLine: Record "Prod. Order Line";
        ProdOrderLineDetail: Record "Prod. Order Line Detail IPK";

        ProdOrderExeededErr: Label 'You exeeded the production orders Max Available Quantity.';
    begin
        IpekPamukSetup.GetRecordOnce();
        if TempPackageCreation."Max Available Quantity" < (TempPackageCreation."Package Count" * TempPackageCreation."Package Quantity") then
            Error(ProdOrderExeededErr);

        ProdOrderLine.Get(ProdOrderLine.Status::Released, TempPackageCreation."Document No.", TempPackageCreation."Document Line No.");
        ProdOrderLine.TestField("Location Code");

        CuttingProdOrderLine.SetRange(Status, ProdOrderLine.Status::Released);
        CuttingProdOrderLine.SetRange("Prod. Order No.", TempPackageCreation."Document No.");
        CuttingProdOrderLine.SetFilter("Item No.", '%1|%2', IpekPamukSetup."Cutting Item No.", IpekPamukSetup."Organic Cutting Item No.");
        if CuttingProdOrderLine.FindFirst() then
            CuttingsRatio := CuttingProdOrderLine.Quantity / ProdOrderLine.Quantity;//

        TempPackageCreation.TestField("Package Count");
        TempPackageCreation.TestField("Package Quantity");

        InventorySetup.GetRecordOnce();
        InventorySetup.TestField("Package Nos.");

        ProdOrderLineDetail.Init();
        ProdOrderLineDetail.Status := ProdOrderLineDetail.Status::Released;
        ProdOrderLineDetail."Prod. Order No." := TempPackageCreation."Document No.";
        ProdOrderLineDetail."Prod. Order Line No." := TempPackageCreation."Document Line No.";
        ProdOrderLineDetail."Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.");
        ProdOrderLineDetail.Validate("Item No.", TempPackageCreation."Item No.");
        ProdOrderLineDetail.Validate("Variant Code", TempPackageCreation."Variant Code");
        ProdOrderLineDetail."Lot No." := TempPackageCreation."Lot No.";
        ProdOrderLineDetail.Quantity := TempPackageCreation."Package Quantity";
        ProdOrderLineDetail.Validate("Location Code", ProdOrderLine."Location Code");
        ProdOrderLineDetail.Insert(true);//en son her paket için ayrı barkod çıkartmama hatası kaldı

        CreatePackageNoInformationFromPackageCreation(TempPackageCreation, ProdOrderLineDetail."Package No.", ProdOrderLine."Location Code", ProdOrderLine."Prod. Order No.", ProdOrderLine."Routing No.", PackageNoInformation);

        CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
        CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail);//alper
        //telef no 'su alanı setupa ekelencek
        //Item carda default production(hedef) location eklenecek,

        PostConsumptionAndOutputJournals(ProdOrderLineDetail);
        IpekQualityManagement.CreateQualityControlForProdOrderLineDetail(ProdOrderLineDetail);
        //Print Pallet label here

        PackageNoInformation.SetRecFilter();
        Commit();//new pack no's needs previous posted Items at their locations
        exit(ProdOrderLineDetail."Package No.");
        // Report.Run(Report::"Palette Label IPK", true, true, PackageNoInformation);
    end;

    procedure CreatePackageNoInformationFromPackageCreation(var TempPackageCreation: Record "Package Creation IPK" temporary; PackageNo: Code[50]; LocationCode: Code[10]; ProdOrderNo: Code[20]; RoutingNo: Code[20]; var PackageNoInformation: Record "Package No. Information")
    var

        Item: Record Item;
        PackageNoInfoLine: Record "Package No. Info. Line IPK";
    begin
        PackageNoInformation.Init();
        // PackageNoInformation."Item No.":=
        // PackageNoInformation."Variant Code" :=;        
        PackageNoInformation."Package No." := PackageNo;
        PackageNoInformation.Validate("Location Code IPK", LocationCode);
        PackageNoInformation."Document No. IPK" := ProdOrderNo;
        PackageNoInformation."Created At IPK" := CurrentDateTime();
        PackageNoInformation."Machine/Line No. IPK" := RoutingNo;
        PackageNoInformation.Validate("Produced By IPK", TempPackageCreation."Produced By");
        PackageNoInformation.Insert(true);

        PackageNoInfoLine.Init();
        PackageNoInfoLine."Package No." := PackageNoInformation."Package No.";
        // PackageNoInfoLine."Item No.":= PackageNoInformation."Item No."
        // PackageNoInfoLine."Variant Code":= PackageNoInformation."Variant Code";
        PackageNoInfoLine.Insert(true);
        PackageNoInfoLine.Validate("Line Item No.", TempPackageCreation."Item No.");
        PackageNoInfoLine.Validate("Line Variant Code", TempPackageCreation."Variant Code");
        PackageNoInfoLine.Validate(Description, TempPackageCreation."Item Description");
        PackageNoInfoLine.Validate(Quantity, TempPackageCreation."Package Quantity");
        PackageNoInfoLine.Validate("Lot No.", TempPackageCreation."Lot No.");
        Item.Get(PackageNoInfoLine."Line Item No.");
        PackageNoInfoLine.Validate("Unit of Measure Code", Item."Base Unit of Measure");
        PackageNoInfoLine.Modify(true);
    end;

    procedure CreatePackageFromProdOrderLine(ProdOrderLine: Record "Prod. Order Line"; PackageCreationMethod: Enum "Package Creation Method IPK")
    var
        Item: Record Item;
        TempPackageCreation: Record "Package Creation IPK" temporary;

    begin
        Item.Get(ProdOrderLine."Item No.");

        TempPackageCreation.Init();
        TempPackageCreation."Source Type" := TempPackageCreation."Source Type"::Production;
        TempPackageCreation."Document No." := ProdOrderLine."Prod. Order No.";
        TempPackageCreation."Document Line No." := ProdOrderLine."Line No.";
        TempPackageCreation.Validate("Item No.", ProdOrderLine."Item No.");
        TempPackageCreation.Validate("Variant Code", ProdOrderLine."Variant Code");

        TempPackageCreation."Lot No." := GetLotNoFromProdOrderLotNos(ProdOrderLine);

        TempPackageCreation.Validate("Item Pieces Of Pallet", Item."Pieces on Pallet IPK");
        TempPackageCreation.Validate("Order Quantity", ProdOrderLine.Quantity);
        TempPackageCreation.Validate("Max Available Quantity", ProdOrderLine.Quantity - ProdOrderLine."Finished Qty. (Base)");//overload yapılıren burada dikkate alınmalı


        TempPackageCreation."Creation Method" := PackageCreationMethod;
        if PackageCreationMethod = PackageCreationMethod::Single then
            TempPackageCreation."Package Count" := 1;

        TempPackageCreation.Validate("Package Quantity", Item."Pieces on Pallet IPK");
        TempPackageCreation.Insert(false);

        Page.Run(Page::"Package Creation IPK", TempPackageCreation);
    end;

    procedure GetShiftStartDateTime(): DateTime
    var
        xDate: Date;
        xDateTime: DateTime;
        // TimeDifference: Duration;
        EightHours: Duration;
        xHour: Integer;
        Time1: Time;
        Time2: Time;
        Time3: Time;
    begin

        IpekPamukSetup.GetRecordOnce();
        // xHour := DT2Time(CurrDT).Hour;
        xHour := CurrentDateTime().Time().Hour();
        EightHours := 8 * 60 * 60 * 1000; // 8 hours in milliseconds
        Time1 := IpekPamukSetup."Shift Start Time";
        Time2 := IpekPamukSetup."Shift Start Time" + EightHours;
        Time3 := IpekPamukSetup."Shift Start Time" + 2 * EightHours;
        if ((xHour) < 7) and ((xHour) >= 0) then begin
            // message('Dün');
            xDate := CurrentDateTime().Date();
            xDate := xDate - 1;
            xDateTime := CreateDateTime(xDate, IpekPamukSetup."Shift Start Time");
            exit(xDateTime);
        end
        else


            // message('Bugün');
            case true of
                (Time2.Hour() < xHour) and (Time3.Hour() > xHour):
                    exit((CreateDateTime(CurrentDateTime().Date(), Time2)));
                (Time3.Hour() < xHour) and (Time1.Hour() > xHour):
                    exit((CreateDateTime(CurrentDateTime().Date(), Time3)));
                else
                    exit((CreateDateTime(CurrentDateTime().Date(), Time1)));
            end;

    end;

    procedure CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail IPK")
    var
        Item: Record Item;
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ProdOrderLine: Record "Prod. Order Line";
        OutputJnlExplRoute: Codeunit "Output Jnl.-Expl. Route";
        // i: Integer;
        // LotNo: Code[50];
        ItemJournalLineNo: Integer;
    // CannotFindGlobalLotErr: Label 'Cannot Find GlobalCuttingLotNo';
    begin

        //     CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail);


        ProdOrderLineDetail.TestField(Posted, false);

        IpekPamukSetup.GetRecordOnce();
        IpekPamukSetup.TestField("Output Journal Template Name");
        IpekPamukSetup.TestField("Output Journal Batch Name");

        ItemJournalLineNo := 10000;
        LastItemJournalLine.SetRange("Journal Template Name", IpekPamukSetup."Output Journal Template Name");
        LastItemJournalLine.SetRange("Journal Batch Name", IpekPamukSetup."Output Journal Batch Name");
        if LastItemJournalLine.FindLast() then
            ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;

        ItemJournalLine.Init();
        ItemJournalLine.Validate("Journal Template Name", IpekPamukSetup."Output Journal Template Name");
        ItemJournalLine.Validate("Journal Batch Name", IpekPamukSetup."Output Journal Batch Name");
        ItemJournalLine.Validate("Line No.", ItemJournalLineNo);
        ItemJournalLine.SetUpNewLine(LastItemJournalLine);
        ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Output);
        ItemJournalLine.Insert(true);

        ItemJournalLine.Validate("Order No.", ProdOrderLineDetail."Prod. Order No.");
        ItemJournalLine.Validate("Item No.", ProdOrderLineDetail."Item No.");
        ItemJournalLine.Validate("Variant Code", ProdOrderLineDetail."Variant Code");

        // ItemJournalLine.Validate("Lot No.", ProdOrderLineDetail."Lot No.");

        //ItemJournalLine.Validate("Package No.", ProdOrderLineDetail."Package No.");
        ItemJournalLine.Modify(true);

        GlobalOutputQty := ProdOrderLineDetail.Quantity;
        GlobalLotNo := ProdOrderLineDetail."Lot No.";
        OutputJnlExplRoute.Run(ItemJournalLine);

        // GlobalLotNo := '';
        GlobalOutputQty := 0;
        //CuttingLineTOJournal
        if CuttingsRatio > 0 then begin
            LastItemJournalLine.SetRange("Journal Template Name", IpekPamukSetup."Output Journal Template Name");
            LastItemJournalLine.SetRange("Journal Batch Name", IpekPamukSetup."Output Journal Batch Name");
            if LastItemJournalLine.FindLast() then
                ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;
            LastItemJournalLine := ItemJournalLine;

            Item.Get(CuttingProdOrderLine."Item No.");

            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine.Validate("Journal Template Name", IpekPamukSetup."Output Journal Template Name");
            ItemJournalLine.Validate("Journal Batch Name", IpekPamukSetup."Output Journal Batch Name");
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine.Validate("Line No.", (ItemJournalLineNo + 10000));
            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Output);
            //alper
            ItemJournalLine.Insert(true);

            ItemJournalLine.Validate("Order No.", CuttingProdOrderLine."Prod. Order No.");
            ItemJournalLine.Validate("Item No.", CuttingProdOrderLine."Item No.");
            ItemJournalLine.Validate("Variant Code", CuttingProdOrderLine."Variant Code");

            ItemJournalLine.Validate("Location Code", IpekPamukSetup."Default Cutting Location");
            ProdOrderLine.Get(ProdOrderLineDetail.Status, ProdOrderLineDetail."Prod. Order No.", ProdOrderLineDetail."Prod. Order Line No.");

            AssignLotNoToItemJournalLine(ItemJournalLine, GlobalLotNo, CuttingsRatio * ProdOrderLineDetail.Quantity, CuttingsRatio * ProdOrderLineDetail.Quantity, '');
            ItemJournalLine.Validate(Quantity, CuttingsRatio * ProdOrderLineDetail.Quantity);
            ItemJournalLine.Validate("Output Quantity (Base)", CuttingsRatio * ProdOrderLineDetail.Quantity);
            ItemJournalLine.Modify(true);
        end;
        //Codeunit.Run(Codeunit::"Item Jnl.-Post Batch", ItemJournalLine);



        // OutputJournal.SetTableView(ItemJournalLine);
        // OutputJournal.Run();
    end;

    //Production orders output journal
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnAfterInsertItemJnlLine, '', false, false)]
    local procedure OnAfterInsertItemJnlLine(var ItemJournalLine: Record "Item Journal Line")
    begin
        if ItemJournalLine.LastOutputOperation(ItemJournalLine) then
            AssignLotNoToItemJournalLine(ItemJournalLine, GlobalLotNo, ItemJournalLine."Output Quantity (Base)", ItemJournalLine."Output Quantity", '')
    end;

    procedure AssignLotNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; LotNo: Code[50]; QtyBase: Decimal; Qty: Decimal; NewLotNo: Code[50])
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        ItemJnlLineReserve: Codeunit "Item Jnl. Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        ItemJnlLineReserve.InitFromItemJnlLine(TempSourceTrackingSpecification, ItemJournalLine);


        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := LotNo;
        TempTrackingSpecification."New Lot No." := NewLotNo;
        // TempTrackingSpecification.Validate("Lot No.", LotNo);
        // TempTrackingSpecification.Validate("New Lot No.", NewLotNo);

        TempTrackingSpecification.SetQuantities(QtyBase,
                                                Qty,
                                                QtyBase,
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);
        ItemTrackingLines.SetBlockCommit(true);
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, ItemJournalLine."Posting Date", TempTrackingSpecification);

    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCreateReservEntryFor, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCreateReservEntryFor"(var OldTrackingSpecification: Record "Tracking Specification"; var NewTrackingSpecification: Record "Tracking Specification"; var CreateReservEntry: Codeunit "Create Reserv. Entry")
    begin
        if OldTrackingSpecification."New Lot No." <> '' then
            CreateReservEntry.SetNewTrackingFromNewTrackingSpecification(OldTrackingSpecification);
    end;


    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCopyTrackingSpec, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCopyTrackingSpec"(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    begin
        DestTrkgSpec.CopyNewTrackingFromNewTrackingSpec(SourceTrackingSpec);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeOutputItemJnlLineInsert, '', false, false)]
    local procedure OnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    begin
        ItemJournalLine.Validate("Output Quantity", GlobalOutputQty);

        // if not ItemJournalLine.LastOutputOperation(ItemJournalLine) then begin
        //     ItemJournalLine.Validate("Lot No.", '');
        //     ItemJournalLine.Validate("Package No.", '');
        // end;
    end;

    procedure CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail IPK")
    var
        // ProductionOrder: Record "Production Order";
        // ItemJournalLine: Record "Item Journal Line";
        // LastItemJournalLine: Record "Item Journal Line";
        // ItemJournalLineNo: Integer;
        ProdOrderComponent: Record "Prod. Order Component";
        CalcConsumption: Report "Calc. Consumption";
        CalcBasedOn: Option "Actual Output","Expected Output";

    begin
        ProdOrderLineDetail.TestField(Posted, false);

        IpekPamukSetup.GetRecordOnce();
        IpekPamukSetup.TestField("Consumption Jnl. Template Name");
        IpekPamukSetup.TestField("Consumption Jnl. Batch Name");

        // ProductionOrder.Get(ProdOrderLineDetail.Status, ProdOrderLineDetail."Prod. Order No.");
        // ProductionOrder.SetRecFilter();

        ProdOrderComponent.SetRange("Prod. Order No.", ProdOrderLineDetail."Prod. Order No.");
        ProdOrderComponent.SetRange("Prod. Order Line No.", ProdOrderLineDetail."Prod. Order Line No.");

        CalcConsumption.UseRequestPage(false);
        // CalcConsumption.SetTableView(ProductionOrder);
        CalcConsumption.SetTableView(ProdOrderComponent);
        CalcConsumption.InitializeRequest(WorkDate(), CalcBasedOn::"Expected Output");
        CalcConsumption.SetTemplateAndBatchName(IpekPamukSetup."Consumption Jnl. Template Name", IpekPamukSetup."Consumption Jnl. Batch Name");
        GlobalOutputQty := ProdOrderLineDetail.Quantity;
        CalcConsumption.RunModal();
        GlobalOutputQty := 0;


        //Page.Run(Page::"Consumption Journal");

        // ItemJournalLineNo := 10000;
        // LastItemJournalLine.SetRange("Journal Template Name", SumikaSetup."Consumption Jnl. Template Name");
        // LastItemJournalLine.SetRange("Journal Batch Name", SumikaSetup."Consumption Jnl. Batch Name");
        // if LastItemJournalLine.FindLast() then
        //     ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;
    end;

    [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnAfterCreateConsumpJnlLine, '', false, false)]
    local procedure "Calc. Consumption_OnAfterCreateConsumpJnlLine"(LocationCode: Code[10]; BinCode: Code[20]; QtyToPost: Decimal; var ItemJournalLine: Record "Item Journal Line")
    var
        Item: Record Item;
        ItemCategory: Record "Item Category";
        LastItemJournalLine: Record "Item Journal Line";
        NewItemJournalLine: Record "Item Journal Line";
        ProductionOrder: Record "Production Order";
        ItemJournalLineNo: Integer;
    begin
        Item.Get(ItemJournalLine."Item No.");
        ProductionOrder.Get(ProductionOrder.Status::Released, ItemJournalLine."Document No.");
        if ItemCategory.Get(Item."Item Category Code") then
            if (ItemCategory."Waterjet IPK") and (ProductionOrder."Source Type" = ProductionOrder."Source Type"::Family) then begin
                IpekPamukSetup.GetRecordOnce();
                LastItemJournalLine.SetRange("Journal Template Name", IpekPamukSetup."Consumption Jnl. Template Name");
                LastItemJournalLine.SetRange("Journal Batch Name", IpekPamukSetup."Consumption Jnl. Batch Name");

                if not LastItemJournalLine.FindLast() then
                    ItemJournalLineNo := 100
                else
                    ItemJournalLineNo := LastItemJournalLine."Line No." + 100;

                NewItemJournalLine.Init();
                NewItemJournalLine.TransferFields(ItemJournalLine);
                // NewItemJournalLine := ItemJournalLine;

                NewItemJournalLine.Validate("Line No.", ItemJournalLineNo);

                //NewItemJournalLine.Validate("Order Line No.", ItemJournalLine."Order Line No." + 10000);
                NewItemJournalLine.Validate(Quantity, GlobalOutputQty * CuttingsRatio);

                NewItemJournalLine.Insert(true);

                AssignItemTrackingInformationFromItemJournalConsumptionLine(NewItemJournalLine, true);
                //Message('A');
            end;
    end;




    //alper
    procedure PostConsumptionAndOutputJournals(var ProdOrderLineDetail: Record "Prod. Order Line Detail IPK")
    var
        ItemJournalLine: Record "Item Journal Line";
        ConsumptionErr: Label 'Something wrong with journal entries, please check Consumption Lines.';
        ProductionErr: Label 'Something wrong with journal entries, please check Production Lines.';
    begin
        IpekPamukSetup.GetRecordOnce();

        ItemJournalLine.SetRange("Journal Template Name", IpekPamukSetup."Consumption Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", IpekPamukSetup."Consumption Jnl. Batch Name");
        if not ItemJournalLine.FindFirst() then
            Error(ConsumptionErr);
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

        ItemJournalLine.Reset();
        ItemJournalLine.SetRange("Journal Template Name", IpekPamukSetup."Output Journal Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", IpekPamukSetup."Output Journal Batch Name");
        if not ItemJournalLine.FindFirst() then
            Error(ProductionErr);
        ItemJournalLine."Location Code" := IpekPamukSetup."Default Cutting Location";
        // ItemJournalLine.Validate("Location Code", IpekPamukSetup."Default Cutting Location");
        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);

        ProdOrderLineDetail.Validate(Posted, true);
        ProdOrderLineDetail.Modify(true);
    end;

    [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnBeforeGetNeededQty, '', false, false)]
    local procedure "Calc. Consumption_OnBeforeGetNeededQty"(var NeededQty: Decimal; CalcBasedOn: Option; ProdOrderComponent: Record "Prod. Order Component"; ProductionOrder: Record "Production Order"; PostingDate: Date; var IsHandled: Boolean)
    begin
        if GlobalOutputQty > 0 then begin
            IsHandled := true;
            NeededQty := ProdOrderComponent."Quantity per" * GlobalOutputQty;
            GlobalLastNeededQty := ProdOrderComponent."Quantity per" * GlobalOutputQty;
        end;
    end;

    procedure CreatePackageFromRoleCenter()
    var
        VarPackageCreation: Record "Package Creation IPK";
    // Item: Record Item;
    // PackageCreation: Page "Package Creation IPK";
    begin
        // Item.Get(ProdOrderLine."Item No.");

        VarPackageCreation.Init();
        VarPackageCreation."Source Type" := VarPackageCreation."Source Type"::Production;
        // TempPackageCreation."Document No." := ProdOrderLine."Prod. Order No.";
        // TempPackageCreation."Document Line No." := ProdOrderLine."Line No.";
        // TempPackageCreation.Validate("Item No.", ProdOrderLine."Item No.");
        // TempPackageCreation."Lot No." := 'LOT24-XXXXXX';
        VarPackageCreation."Creation Method" := Enum::"Package Creation Method IPK"::Single;
        VarPackageCreation."Package Count" := 1;

        // TempPackageCreation.Validate("Package Quantity", Item."Pieces on Pallet IPK");
        VarPackageCreation.Insert(false);

        // TempPackageCreation.SetRecFilter();
        // PackageCreation.SetRecordOnPage(TempPackageCreation);
        // PackageCreation.OpenFromRoleCenter(true);
        // PackageCreation.RunModal();

        Page.RunModal(Page::"Package Creation IPK", VarPackageCreation);

    end;

    procedure CreatePaletteFromRoleCenter()
    var
        InventorySetup: Record "Inventory Setup";
        PackageNoInformation: Record "Package No. Information";
        PageManagement: Codeunit "Page Management";
    // Item: Record Item;
    // PackageCreation: Page "Package Creation IPK";
    begin

        InventorySetup.GetRecordOnce();
        PackageNoInformation.Init();
        PackageNoInformation."Pallet IPK" := true;
        PackageNoInformation."Item No." := '';
        PackageNoInformation."Variant Code" := '';
        PackageNoInformation."Package No." := NoSeries.GetNextNo(InventorySetup."Package Nos.");
        PackageNoInformation.Insert(true);


        PageManagement.PageRun(PackageNoInformation);
        // Page.RunModal(Page::"Palette Card IPK", PackageNoInformation);

    end;

    // procedure AddLineToPackageNoInfoLine()
    // begin
    // end;

    procedure GetLotNoFromProdOrderLotNos(var ProdOrderLine: Record "Prod. Order Line"): Code[50]
    var
        ProdOrderLineLotNo: Record "Prod.Order Line - Lot No. IPK";
        NewEndDt: DateTime;
        //TempDT: DateTime;
        NewStartDt: DateTime;
    begin
        ProdOrderLineLotNo.SetRange(Status, ProdOrderLine.Status);
        ProdOrderLineLotNo.SetRange("Prod. Order No.", ProdOrderLine."Prod. Order No.");
        ProdOrderLineLotNo.SetRange("Prod. Order Line No.", ProdOrderLine."Line No.");

        if ProdOrderLineLotNo.FindLast() then begin
            //TempDT := ProdOrderLineLotNo."End Date-Time";

            if not ((ProdOrderLineLotNo."Start Date-Time" <= CurrentDateTime()) and (ProdOrderLineLotNo."End Date-Time" > CurrentDateTime())) then begin

                NewStartDt := ProdOrderLineLotNo."Start Date-Time";
                NewEndDt := ProdOrderLineLotNo."End Date-Time";//ProdOrderLineLotNo."Start Date-Time" + (8 * 3600000);
                while not ((NewStartDt <= CurrentDateTime()) and (NewEndDt > CurrentDateTime())) do begin
                    NewStartDt += (8 * 3600000);
                    NewEndDt += (8 * 3600000);
                end;

                ProdOrderLineLotNo.Init();
                ProdOrderLineLotNo.Status := ProdOrderLine.Status;
                ProdOrderLineLotNo."Prod. Order No." := ProdOrderLine."Prod. Order No.";
                ProdOrderLineLotNo."Prod. Order Line No." := ProdOrderLine."Line No.";

                ProdOrderLineLotNo.Validate("Start Date-Time", NewStartDt);
                ProdOrderLineLotNo.Insert(true);
            end;


        end
        else begin
            ProdOrderLineLotNo.Init();
            ProdOrderLineLotNo.Status := ProdOrderLine.Status;
            ProdOrderLineLotNo."Prod. Order No." := ProdOrderLine."Prod. Order No.";
            ProdOrderLineLotNo."Prod. Order Line No." := ProdOrderLine."Line No.";
            ProdOrderLineLotNo.Validate("Start Date-Time", GetShiftStartDateTime());
            ProdOrderLineLotNo.Insert(true);
        end;
        exit(ProdOrderLineLotNo."Lot No.");
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnCreateProductionOrderOnBeforeProdOrderLineModify, '', false, false)]
    local procedure "Create Prod. Order from Sale_OnCreateProductionOrderOnBeforeProdOrderLineModify"(var ProdOrderLine: Record "Prod. Order Line"; var SalesLine: Record "Sales Line"; var ProdOrder: Record "Production Order"; var SalesLineReserve: Codeunit "Sales Line-Reserve")
    begin

        HandleReserveEntry(SalesLine);

    end;



    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order from Sale", OnAfterCreateProdOrderFromSalesLine, '', false, false)]
    // local procedure "Create Prod. Order from Sale_OnAfterCreateProdOrderFromSalesLine"(var ProdOrder: Record "Production Order"; var SalesLine: Record "Sales Line")
    // begin
    //     ProdOrder."Location Code" := 'UHD';//Setupa eklenecek
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales Line-Reserve", OnCreateReservationOnBeforeTestLocationCode, '', false, false)]
    local procedure "Sales Line-Reserve_OnCreateReservationOnBeforeTestLocationCode"(SalesLine: Record "Sales Line"; FromTrackingSpecification: Record "Tracking Specification"; var IsHandled: Boolean)
    begin
        IsHandled := true;
    end;

    procedure CreateProdOrderfromSale_OnAfterCreateProdOrderFromSalesLine(var ProdOrder: Record "Production Order"; var SalesLine: Record "Sales Line")

    begin
        ProdOrder."Source Document No. IPK" := SalesLine."Document No.";
        ProdOrder."Source Document Line No. IPK" := SalesLine."Line No.";


        DecideProdOrderSourceType(ProdOrder, SalesLine);


    end;

    procedure AssignItemTrackingInformationFromItemJournalConsumptionLine(var ItemJnlLine: Record "Item Journal Line"; IsCutting: Boolean)
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        DynamicQty: Decimal;
        // ILERemainingQuantity: Decimal;
        TempGlobalQty: Decimal;
        QtyIsNotEnaughErr: Label 'Item No.: %1, Variant Code: %2 - %4 is not enough in %3', Comment = 'Item No.: %1, Variant Code: %2 is not enaugh in %3, %4 - Description';
    begin




        ItemLedgerEntry.SetRange("Item No.", ItemJnlLine."Item No.");//#alper
        ItemLedgerEntry.SetRange("Variant Code", ItemJnlLine."Variant Code");
        ItemLedgerEntry.SetRange("Location Code", ItemJnlLine."Location Code");
        ItemLedgerEntry.SetRange(Open, true);
        ItemLedgerEntry.SetCurrentKey("Lot No.");

        // if IsCutting then
        ItemLedgerEntry.SetAscending("Lot No.", true);

        DynamicQty := ItemJnlLine."Quantity (Base)";// + GlobalOutputQty;
        if IsCutting then
            TempGlobalQty := GlobalLastNeededQty;
        if ItemLedgerEntry.FindSet() then
            repeat

                //Skip First GlobalOutputQty amount at ItemLedgerEntry
                if TempGlobalQty <> 0 then
                    if TempGlobalQty > ItemLedgerEntry."Remaining Quantity" then begin
                        TempGlobalQty -= ItemLedgerEntry."Remaining Quantity";
                        continue;
                    end;
                // else begin
                //     ILERemainingQuantity := ItemLedgerEntry."Remaining Quantity" - TempGlobalQty;
                //     //TempGlobalQty := 0;
                // end;
                //alper

                if (DynamicQty > ItemLedgerEntry."Remaining Quantity" - TempGlobalQty) then begin
                    AssignLotNoToItemJournalLine(ItemJnlLine, ItemLedgerEntry."Lot No.", ItemLedgerEntry."Remaining Quantity" - TempGlobalQty, ItemLedgerEntry."Remaining Quantity" - TempGlobalQty, '');
                    DynamicQty -= ItemLedgerEntry."Remaining Quantity" - TempGlobalQty;
                    if IsCutting then begin
                        GlobalCuttingLotNo.Add(ItemLedgerEntry."Lot No.");
                        GlobalCuttingQuantities.Add(ItemLedgerEntry."Remaining Quantity" - TempGlobalQty);
                        TempGlobalQty := 0;
                    end;
                end
                else begin

                    AssignLotNoToItemJournalLine(ItemJnlLine, ItemLedgerEntry."Lot No.", DynamicQty, DynamicQty, '');


                    if IsCutting then begin
                        GlobalCuttingLotNo.Add(ItemLedgerEntry."Lot No.");
                        GlobalCuttingQuantities.Add(DynamicQty);
                    end;
                    DynamicQty := 0;
                    break;
                end;

            until (ItemLedgerEntry.Next() = 0);

        if DynamicQty <> 0 then
            Error(QtyIsNotEnaughErr, ItemJnlLine."Item No.", ItemJnlLine."Variant Code", ItemJnlLine."Location Code", ItemJnlLine.Description);//TELEF YETERSİZ İFADESİ EKLE
    end;

    procedure SetLocationToProdOrderComponent(var ProdOrderLine: Record "Prod. Order Line"; var ProdOrderComponent: Record "Prod. Order Component")
    var
        Item: Record Item;
        ItemCategory: Record "Item Category";
    begin
        Item.Get(ProdOrderLine."Item No.");
        ItemCategory.Get(Item."Item Category Code");

        repeat
            if ItemCategory."Default Consumption Loc. IPK" <> '' then
                break;

            ItemCategory.Get(ItemCategory."Parent Category");
        until ItemCategory."Default Consumption Loc. IPK" <> '';

        ProdOrderComponent.Validate("Location Code", ItemCategory."Default Consumption Loc. IPK");
    end;

    local procedure DecideProdOrderSourceType(var ProdOrder: Record "Production Order"; var SalesLine: Record "Sales Line")
    var
        Family: Record Family;
    begin
        if Family.Get(SalesLine."No.") then begin
            ProdOrder."Source Type" := ProdOrder."Source Type"::Family;
            ProdOrder.Validate("Source No.", SalesLine."No.");
            ProdOrder.Modify(true);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create Prod. Order Lines", OnAfterProdOrderLineInsert, '', false, false)]
    local procedure "Create Prod. Order Lines_OnAfterProdOrderLineInsert"(var Sender: Codeunit "Create Prod. Order Lines"; var ProdOrder: Record "Production Order"; var ProdOrderLine: Record "Prod. Order Line"; var NextProdOrderLineNo: Integer)
    begin
        SetDefaultOutputLocation(ProdOrderLine);
    end;


    local procedure SetDefaultOutputLocation(var ProdOrderLine: Record "Prod. Order Line")
    var
        Item: Record Item;
    begin
        Item.Get(ProdOrderLine."Item No.");
        if Item."Default Output Location IPK" <> '' then begin
            ProdOrderLine.Validate("Location Code", Item."Default Output Location IPK");
            ProdOrderLine.Modify(true);
        end;
    end;

    local procedure HandleReserveEntry(var SalesLine: Record "Sales Line")
    var
        ReservEntry: Record "Reservation Entry";
        ReservEngineMgt: Codeunit "Reservation Engine Mgt.";
    begin
        ReservEntry.SetRange("Reservation Status", ReservEntry."Reservation Status"::Reservation);
        ReservEntry.SetRange("Source Type", Database::"Sales Line");
        ReservEntry.SetRange("Source Subtype", SalesLine."Document Type"::Order);
        ReservEntry.SetRange("Source ID", SalesLine."Document No.");
        ReservEntry.SetRange("Source Ref. No.", SalesLine."Line No.");
        if not ReservEntry.FindSet() then exit;


        repeat
            ReservEntry.TestField("Reservation Status", ReservEntry."Reservation Status"::Reservation);
            ReservEntry.TestField("Disallow Cancellation", false);

            ReservEngineMgt.CancelReservation(ReservEntry);

        until ReservEntry.Next() = 0;
    end;

    var
        IpekPamukSetup: Record "Ipek Pamuk Setup IPK";
        CuttingProdOrderLine: Record "Prod. Order Line";
        IpekQualityManagement: Codeunit "Ipek Quality Management IPK";
        NoSeries: Codeunit "No. Series";
        GlobalLotNo: Code[50];
        CuttingsRatio: Decimal;
        GlobalLastNeededQty: Decimal;
        GlobalOutputQty: Decimal;

        GlobalCuttingLotNo: List of [Code[50]];
        GlobalCuttingQuantities: List of [Decimal];
}